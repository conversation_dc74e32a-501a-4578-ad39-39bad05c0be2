package com.haihang.service.outbound.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haihang.factory.QualityInspectionHandlerFactory;
import com.haihang.handler.record.QualityInspectionHandler;
import com.haihang.mapper.application.QualityInspectionApplicationMapper;
import com.haihang.mapper.application.StaffMapper;
import com.haihang.mapper.common.CompanyMapper;
import com.haihang.mapper.common.GroupMapper;
import com.haihang.mapper.common.ProductTypeForSortMapper;
import com.haihang.mapper.outbound.*;
import com.haihang.mapper.record.BasicInformationMapper;
import com.haihang.mapper.record.BasicInformationNotSavedMapper;
import com.haihang.mapper.record.ItemPriorityForSortMapper;
import com.haihang.mapper.additionalRequirement.AdditionalRequirementCustomerMapper;
import com.haihang.mapper.additionalRequirement.AdditionalRequirementDetailsMapper;
import com.haihang.mapper.additionalRequirement.AdditionalRequirementMapper;
import com.haihang.mapper.additionalRequirement.AdditionalRequirementProductCategoryMapper;
import com.haihang.mapper.requirementMaintenance.GeneralQualityStandardMapper;
import com.haihang.mapper.requirementMaintenance.GroupQualityStandardMapper;
import com.haihang.mapper.requirementMaintenance.MaterialInformationMapper;
import com.haihang.mapper.requirementMaintenance.MaterialCategoryMapper;
import com.haihang.mapper.common.CommonCompanyMapper;
import com.haihang.mapper.user.UserMapper;
import com.haihang.model.DO.common.Company;
import com.haihang.model.DO.common.CommonCompany;
import com.haihang.model.DO.common.Group;
import com.haihang.model.DO.common.ProductTypeForSort;
import com.haihang.model.DO.common.Staff;
import com.haihang.model.DO.requirementMaintenance.MaterialCategory;
import com.haihang.model.DO.requirementMaintenance.MaterialInformation;
import com.haihang.model.DO.outbound.*;
import com.haihang.model.DO.record.BasicInformation;
import com.haihang.model.DO.record.BasicInformationNotSaved;
import com.haihang.model.DO.record.ItemPriorityForSort;
import com.haihang.model.DO.requirementMaintenance.GeneralQualityStandard;
import com.haihang.model.DO.requirementMaintenance.GroupQualityStandard;
import com.haihang.model.DO.additionalRequirement.AdditionalRequirementCustomer;
import com.haihang.model.DO.additionalRequirement.AdditionalRequirementDetails;
import com.haihang.model.DO.additionalRequirement.AdditionalRequirementProductCategory;
import com.haihang.model.DO.additionalRequirement.TransportReportAdditionalRequirement;
import com.haihang.model.DO.application.QualityInspectionApplication;
import com.haihang.model.DO.user.User;
import com.haihang.model.DTO.elementUI.SelectLabelDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordDetailsDTO;
import com.haihang.model.DTO.outbound.transferRecord.TransferRecordProductionAndQualityDataDTO;
import com.haihang.model.DTO.outbound.transportReport.*;

import com.haihang.model.Query.outbound.TransferRecordQuery;
import com.haihang.service.outbound.TransferRecordService;
import com.haihang.utils.record.ItemCalculator;
import com.spire.xls.*;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.security.PdfEncryptionKeySize;
import com.spire.pdf.security.PdfPermissionsFlags;
import com.haihang.mapper.additionalRequirement.AdditionalRequirementTemplateMapper;
import com.haihang.model.DO.additionalRequirement.AdditionalRequirementTemplate;
import java.util.EnumSet;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.security.SecureRandom;
import java.util.Base64;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.idev.excel.FastExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.write.metadata.WriteSheet;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Collections;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.ByteArrayOutputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.Zip64Mode;

/**
 * @Description: @Author: zad @Create: 2024/9/21 08:20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransferRecordServiceImpl implements TransferRecordService {
    private final OutboundInformationTransferRecordMapper transferRecordMapper;
    private final TransportQualityInspectionReportMapper transportQualityInspectionReportMapper;
    private final TransportQualityInspectionDataMapper transportQualityInspectionDataMapper;
    private final BasicInformationMapper basicInformationMapper;
    private final BasicInformationNotSavedMapper basicInformationNotSavedMapper;
    private final MaterialInformationMapper materialInformationMapper;
    private final ProductTypeForSortMapper productTypeForSortMapper;
    private final ItemPriorityForSortMapper itemPriorityForSortMapper;
    private final GroupQualityStandardMapper groupQualityStandardMapper;
    private final CompanyMapper companyMapper;
    private final GroupMapper groupMapper;
    private final UserMapper userMapper;
    private final StaffMapper staffMapper;
    private final TransportReportTemplateMapper transportReportTemplateMapper;
    private final InspectionItemReferenceMapper inspectionItemReferenceMapper;
    private final GeneralQualityStandardMapper generalQualityStandardMapper;
    private final AdditionalRequirementProductCategoryMapper additionalRequirementProductCategoryMapper;
    private final AdditionalRequirementCustomerMapper additionalRequirementCustomerMapper;
    private final QualityInspectionApplicationMapper applicationMapper;
    private final AdditionalRequirementDetailsMapper additionalRequirementDetailsMapper;
    private final AdditionalRequirementMapper additionalRequirementMapper;
    private final AdditionalRequirementTemplateMapper additionalRequirementTemplateMapper;
    private final TransportReportItemI18nMapper transportReportItemI18nMapper;
    private final TransportReportOnlineAvailabilityMapper transportReportOnlineAvailabilityMapper;
    private final TransportReportDownloadRecordMapper transportReportDownloadRecordMapper;
    private final TransportReportCompanyMapper transportReportCompanyMapper;
    private final TransportReportGroupMapper transportReportGroupMapper;
    private final TransportReportItemTestMethodMapper transportReportItemTestMethodMapper;
    private final CommonCompanyMapper commonCompanyMapper;
    private final MaterialCategoryMapper materialCategoryMapper;

    @Value("${path-config.file-path}")
    private String filePath;

    @Override
    public IPage<TransferRecordDTO> getTransferRecordPage(
            int current, int size, TransferRecordQuery transferRecordQuery) {
        List<TransportReportOnlineAvailability> onlineAvailabilityList = new ArrayList<>();
        if (ObjUtil.isNotNull(transferRecordQuery.getOnlineAvailable())) {
            onlineAvailabilityList = transportReportOnlineAvailabilityMapper.selectList(
                    new LambdaQueryWrapper<TransportReportOnlineAvailability>()
                            .eq(TransportReportOnlineAvailability::getOnlineAvailable,
                                    transferRecordQuery.getOnlineAvailable()));
        }

        // 获取所有包含指定生产批号或质检批号的传递单ID
        List<Integer> transferRecordIdsByBatch = new ArrayList<>();
        if (StrUtil.isNotBlank(transferRecordQuery.getProductionBatch()) || 
            StrUtil.isNotBlank(transferRecordQuery.getQualityInspectionBatch())) {
            
            transferRecordIdsByBatch = transferRecordMapper.selectList(
                    new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                            .like(StrUtil.isNotBlank(transferRecordQuery.getProductionBatch()),
                                    OutboundInformationTransferRecord::getOutboundProductionBatch,
                                    transferRecordQuery.getProductionBatch())
                            .or()
                            .like(StrUtil.isNotBlank(transferRecordQuery.getQualityInspectionBatch()),
                                    OutboundInformationTransferRecord::getQualityInspectionBatch,
                                    transferRecordQuery.getQualityInspectionBatch()))
                    .stream()
                    .map(OutboundInformationTransferRecord::getId)
                    .collect(Collectors.toList());
            
            // 如果没有找到符合批号条件的记录，则返回空结果
            if (CollUtil.isEmpty(transferRecordIdsByBatch) && 
                (StrUtil.isNotBlank(transferRecordQuery.getProductionBatch()) || 
                 StrUtil.isNotBlank(transferRecordQuery.getQualityInspectionBatch()))) {
                return new Page<>(current, size);
            }
        }

        List<OutboundInformationTransferRecord> filteredList = new ArrayList<>();
        if (CollUtil.isNotEmpty(onlineAvailabilityList)) {
            // 收集所有的查询条件
            List<String> customerNumbers = onlineAvailabilityList.stream()
                    .map(TransportReportOnlineAvailability::getCustomerNumber)
                    .collect(Collectors.toList());

            List<String> productCategoryNumbers = onlineAvailabilityList.stream()
                    .map(TransportReportOnlineAvailability::getProductCategoryNumber)
                    .collect(Collectors.toList());

            // 一次性查询所有符合条件的记录
            filteredList = transferRecordMapper.selectList(
                    new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                            .in(OutboundInformationTransferRecord::getCustomerNumber, customerNumbers)
                            .in(OutboundInformationTransferRecord::getProductCategoryNumber, productCategoryNumbers));

            // 如果需要精确匹配客户号和产品类别号的组合,可以在内存中过滤
            Set<String> validCombinations = onlineAvailabilityList.stream()
                    .map(a -> a.getCustomerNumber() + "_" + a.getProductCategoryNumber())
                    .collect(Collectors.toSet());

            filteredList = filteredList.stream()
                    .filter(record -> validCombinations.contains(
                            record.getCustomerNumber() + "_" + record.getProductCategoryNumber()))
                    .collect(Collectors.toList());
        } else {
            OutboundInformationTransferRecord outboundInformationTransferRecord = new OutboundInformationTransferRecord();
            outboundInformationTransferRecord.setId(0);
            filteredList.add(outboundInformationTransferRecord);
        }

        // 使用 filteredList 的 ID 列表作为查询条件
        IPage<OutboundInformationTransferRecord> transferRecordPage = transferRecordMapper.selectPage(
                new Page<>(current, size),
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .in(CollUtil.isNotEmpty(filteredList)
                                && ObjUtil.isNotNull(transferRecordQuery.getOnlineAvailable()),
                                OutboundInformationTransferRecord::getId,
                                filteredList.stream().map(OutboundInformationTransferRecord::getId)
                                        .collect(Collectors.toList()))
                        .in(CollUtil.isNotEmpty(transferRecordIdsByBatch),
                                OutboundInformationTransferRecord::getId,
                                transferRecordIdsByBatch)
                        .in(CollUtil.isNotEmpty(transferRecordQuery.getLinkId()),
                                OutboundInformationTransferRecord::getLinkId,
                                transferRecordQuery.getLinkId())
                        .like(StrUtil.isNotBlank(transferRecordQuery.getCustomerName()),
                                OutboundInformationTransferRecord::getCustomerName,
                                transferRecordQuery.getCustomerName())
                        .like(StrUtil.isNotBlank(transferRecordQuery.getProductName()),
                                OutboundInformationTransferRecord::getProductName,
                                transferRecordQuery.getProductName())
                        .isNull(ObjUtil.isNotNull(transferRecordQuery.getHandleStatus())
                                && transferRecordQuery.getHandleStatus() == 0,
                                OutboundInformationTransferRecord::getHandleStatus)
                        .eq(ObjUtil.isNotNull(transferRecordQuery.getHandleStatus())
                                && transferRecordQuery.getHandleStatus() == 1,
                                OutboundInformationTransferRecord::getHandleStatus,
                                transferRecordQuery.getHandleStatus())
                        .isNull(ObjUtil.isNotNull(transferRecordQuery.getFirstReviewStatus())
                                && transferRecordQuery.getFirstReviewStatus() == -1,
                                OutboundInformationTransferRecord::getFirstReviewStatus)
                        .eq(ObjUtil.isNotNull(transferRecordQuery.getFirstReviewStatus())
                                && transferRecordQuery.getFirstReviewStatus() != -1,
                                OutboundInformationTransferRecord::getFirstReviewStatus,
                                transferRecordQuery.getFirstReviewStatus())
                        .isNull(ObjUtil.isNotNull(transferRecordQuery.getSecondReviewStatus())
                                && transferRecordQuery.getSecondReviewStatus() == -1,
                                OutboundInformationTransferRecord::getSecondReviewStatus)
                        .eq(ObjUtil.isNotNull(transferRecordQuery.getSecondReviewStatus())
                                && transferRecordQuery.getSecondReviewStatus() != -1,
                                OutboundInformationTransferRecord::getSecondReviewStatus,
                                transferRecordQuery.getSecondReviewStatus())
                        .ge(StrUtil.isNotBlank(transferRecordQuery.getStartDate()),
                                OutboundInformationTransferRecord::getOutboundDate,
                                transferRecordQuery.getStartDate())
                        .le(StrUtil.isNotBlank(transferRecordQuery.getEndDate()),
                                OutboundInformationTransferRecord::getOutboundDate,
                                transferRecordQuery.getEndDate())
                        .groupBy(OutboundInformationTransferRecord::getOutboundNumber,
                                OutboundInformationTransferRecord::getProductName)
                        .orderByDesc(OutboundInformationTransferRecord::getId));

        // 转换为DTO
        IPage<TransferRecordDTO> transferRecordDTOPage = transferRecordPage
                .convert(result -> BeanUtil.copyProperties(result, TransferRecordDTO.class));

        // 收集所有客户编号和产品类别编号
        List<String> customerNumbers = transferRecordDTOPage.getRecords().stream()
                .map(TransferRecordDTO::getCustomerNumber)
                .distinct()
                .collect(Collectors.toList());

        List<String> productCategoryNumbers = transferRecordDTOPage.getRecords().stream()
                .map(TransferRecordDTO::getProductCategoryNumber)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(customerNumbers) && CollUtil.isNotEmpty(productCategoryNumbers)) {
            // 批量查询线上开单权限
            List<TransportReportOnlineAvailability> availabilityList = transportReportOnlineAvailabilityMapper
                    .selectList(
                            new LambdaQueryWrapper<TransportReportOnlineAvailability>()
                                    .in(TransportReportOnlineAvailability::getCustomerNumber, customerNumbers)
                                    .in(TransportReportOnlineAvailability::getProductCategoryNumber,
                                            productCategoryNumbers));

            // 创建复合key的Map: customerNumber_productCategoryNumber -> availability
            Map<String, TransportReportOnlineAvailability> availabilityMap = availabilityList.stream()
                    .collect(Collectors.toMap(
                            availability -> availability.getCustomerNumber() + "_"
                                    + availability.getProductCategoryNumber(),
                            availability -> availability,
                            (v1, v2) -> v1));

            // 设置线上开单权限
            transferRecordDTOPage.getRecords().forEach(dto -> {
                String key = dto.getCustomerNumber() + "_" + dto.getProductCategoryNumber();
                TransportReportOnlineAvailability availability = availabilityMap.get(key);
                if (ObjUtil.isNotNull(availability)) {
                    dto.setOnlineAvailable(availability.getOnlineAvailable());
                }
            });
        }

        // 查询操作人
        List<Integer> userIdList = transferRecordDTOPage.getRecords().stream()
                .map(TransferRecordDTO::getAddUserId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(userIdList)) {
            List<User> userList = userMapper.selectList(new LambdaQueryWrapper<User>().in(User::getId, userIdList));
            Map<Integer, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));
            transferRecordDTOPage.getRecords().forEach(dto -> {
                User user = userMap.get(dto.getAddUserId());
                if (ObjUtil.isNotNull(user)) {
                    dto.setAddUserName(user.getUsername());
                }
            });
        }
        
        // 查询每个出库单的批号信息
        if (CollUtil.isNotEmpty(transferRecordDTOPage.getRecords())) {
            List<String> outboundNumbers = transferRecordDTOPage.getRecords().stream()
                    .map(TransferRecordDTO::getOutboundNumber)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<String> productNames = transferRecordDTOPage.getRecords().stream()
                    .map(TransferRecordDTO::getProductName)
                    .distinct()
                    .collect(Collectors.toList());
                    
            // 查询所有相关的批号信息
            List<OutboundInformationTransferRecord> batchInfoRecords = transferRecordMapper.selectList(
                    new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                            .in(OutboundInformationTransferRecord::getOutboundNumber, outboundNumbers)
                            .in(OutboundInformationTransferRecord::getProductName, productNames)
                            .select(
                                OutboundInformationTransferRecord::getOutboundNumber,
                                OutboundInformationTransferRecord::getProductName,
                                OutboundInformationTransferRecord::getOutboundProductionBatch,
                                OutboundInformationTransferRecord::getQualityInspectionBatch
                            ));
            
            // 按出库单号和产品名称分组
            Map<String, List<OutboundInformationTransferRecord>> batchInfoMap = batchInfoRecords.stream()
                    .collect(Collectors.groupingBy(
                            record -> record.getOutboundNumber() + "_" + record.getProductName()));
            
            // 为每条记录添加批号信息
            transferRecordDTOPage.getRecords().forEach(dto -> {
                String key = dto.getOutboundNumber() + "_" + dto.getProductName();
                List<OutboundInformationTransferRecord> batchRecords = batchInfoMap.get(key);
                
                if (CollUtil.isNotEmpty(batchRecords)) {
                    List<Map<String, String>> batchInfoList = new ArrayList<>();
                    
                    // 去重处理批号信息
                    Set<String> uniqueBatchCombinations = new HashSet<>();
                    
                    for (OutboundInformationTransferRecord record : batchRecords) {
                        String batchCombo = record.getOutboundProductionBatch() + "_" + record.getQualityInspectionBatch();
                        if (uniqueBatchCombinations.add(batchCombo)) {
                            Map<String, String> batchInfo = new HashMap<>();
                            batchInfo.put("productionBatch", record.getOutboundProductionBatch());
                            batchInfo.put("qualityInspectionBatch", record.getQualityInspectionBatch());
                            batchInfoList.add(batchInfo);
                        }
                    }
                    
                    dto.setBatchInfo(batchInfoList);
                }
            });
        }
        
        return transferRecordDTOPage;
    }

    @Override
    public TransferRecordDetailsDTO getTransferRecordDetails(TransferRecordDTO transferRecordDTO) {
        TransferRecordDetailsDTO transferRecordDetailsDTO = new TransferRecordDetailsDTO();
        // 查询出库信息
        List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transferRecordDTO.getLinkId())
                        .eq(
                                OutboundInformationTransferRecord::getOutboundNumber,
                                transferRecordDTO.getOutboundNumber())
                        .eq(
                                OutboundInformationTransferRecord::getProductNumber,
                                transferRecordDTO.getProductNumber()));
        // 质检申请id集合
        List<Integer> applicationIdList = CollUtil.distinct(
                CollUtil.map(
                        transferRecordList, OutboundInformationTransferRecord::getApplicationId, true));
        // 计算所有 OutboundInformation 的 weight 总和
        BigDecimal totalWeight = transferRecordList.stream()
                .map(OutboundInformationTransferRecord::getWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 查询 BasicInformation 列表并按applicationId分组,每组保留id最大的记录
        final List<BasicInformation> basicInformationList = basicInformationMapper.selectList(
                new LambdaQueryWrapper<BasicInformation>()
                        .in(BasicInformation::getApplicationId, applicationIdList)
                        .eq(BasicInformation::getIsDeleted, false))
                .stream()
                .collect(Collectors.groupingBy(BasicInformation::getApplicationId))
                .values()
                .stream()
                .map(list -> list.stream()
                        .max(Comparator.comparing(BasicInformation::getId))
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 查询 BasicInformationNotSaved 列表
        List<Integer> notSubmittedRecordIdList = CollUtil.map(basicInformationList,
                BasicInformation::getNotSubmittedRecordId, true);
        List<BasicInformationNotSaved> basicInformationNotSavedList = basicInformationNotSavedMapper.selectList(
                new LambdaQueryWrapper<BasicInformationNotSaved>()
                        .in(BasicInformationNotSaved::getId, notSubmittedRecordIdList)
                        .eq(BasicInformationNotSaved::getCommitStatus, true)
                        .eq(BasicInformationNotSaved::getIsDeleted, false));
        // 根据 OutboundInformation 的 outboundProductionBatch 属性对其进行分组
        Map<String, List<OutboundInformationTransferRecord>> groupedList = transferRecordList.stream()
                .collect(
                        Collectors.groupingBy(
                                OutboundInformationTransferRecord::getOutboundProductionBatch));
        // 遍历分组后的 groupedList 和 basicInformationGroupMap,为每个 productionBatch
        // 创建一个OutboundInformationVO对象
        List<TransferRecordProductionAndQualityDataDTO> productionAndQualityDataDTOList = groupedList.entrySet()
                .stream()
                .map(
                        entry -> {
                            TransferRecordProductionAndQualityDataDTO productionAndQualityDataDTO = new TransferRecordProductionAndQualityDataDTO();
                            List<TransferRecordDTO> transferRecordDTOList = new ArrayList<>();
                            TransferRecordDTO transferRecord = new TransferRecordDTO();
                            // 设置执行标准
                            transferRecord.setExecutionStandard(
                                    basicInformationList.get(0).getSpecificationBasis());
                            // 设置实际生产批号
                            List<String> outboundProductionBatchList = entry.getValue().stream()
                                    .map(OutboundInformationTransferRecord::getOutboundProductionBatch)
                                    .filter(StrUtil::isNotBlank)
                                    .distinct()
                                    .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(outboundProductionBatchList)
                                    && outboundProductionBatchList.size() == 1) {
                                transferRecord.setOutboundProductionBatch(outboundProductionBatchList.get(0));
                            } else {
                                transferRecord.setOutboundProductionBatch(entry.getKey());
                            }
                            // 计算该 productionBatch 下所有 transferRecord 的 weight 总和
                            BigDecimal weight = entry.getValue().stream()
                                    .map(OutboundInformationTransferRecord::getWeight)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            transferRecord.setWeight(weight);
                            // 设置总数量(吨)
                            transferRecord.setTotalWeight(totalWeight);

                            // 设置 outboundDate/ourContractNumber/theirContractNumber
                            transferRecord.setOutboundDate(transferRecordDTO.getOutboundDate());
                            transferRecord.setOurContractNumber(transferRecordDTO.getOurContractNumber());
                            transferRecord.setTheirContractNumber(transferRecordDTO.getTheirContractNumber());
                            // 查询物料信息，设置包装规格
                            MaterialInformation materialInformation = materialInformationMapper.selectOne(
                                    new LambdaQueryWrapper<MaterialInformation>()
                                            .eq(
                                                    MaterialInformation::getMaterialNumber,
                                                    transferRecordDTO.getProductNumber()));
                            transferRecord.setProductionPackagingSpecification(
                                    materialInformation.getPackagingSpecification().toUpperCase());
                            String packagingSpecification = transferRecord.getProductionPackagingSpecification();
                            String packagingNumbers = ReUtil.findAll("[0-9]+", packagingSpecification, 0).stream()
                                    .reduce("", (a, b) -> a + b);
                            BigDecimal totalQuantity = weight
                                    .multiply(new BigDecimal(1000))
                                    .divide(new BigDecimal(packagingNumbers), RoundingMode.HALF_UP);
                            transferRecord.setQuantity(totalQuantity);
                            transferRecord.setProductName(transferRecordDTO.getProductName());
                            transferRecordDTOList.add(transferRecord);
                            productionAndQualityDataDTO.setTransferRecordList(transferRecordDTOList);
                            /*
                             * // 根据当前 productionBatch 筛选出对应的 BasicInformation 和 BasicInformationNotSaved
                             * List<BasicInformation> batchBasicInformationList =
                             * basicInformationGroupMap.get(entry.getKey());
                             * List<BasicInformationNotSaved> batchBasicInformationNotSavedList =
                             * basicInformationNotSavedList.stream()
                             * .filter(
                             * item ->
                             * batchBasicInformationList.stream()
                             * .anyMatch(
                             * bi -> bi.getNotSubmittedRecordId().equals(item.getId())))
                             * .collect(Collectors.toList());
                             */
                            List<Integer> outboundApplicationIdList = entry.getValue().stream()
                                    .map(OutboundInformationTransferRecord::getApplicationId)
                                    .collect(Collectors.toList());
                            List<BasicInformation> batchBasicInformationList = basicInformationList.stream()
                                    .filter(basicInformation -> outboundApplicationIdList
                                            .contains(basicInformation.getApplicationId()))
                                    .collect(Collectors.toList());
                            List<BasicInformationNotSaved> batchBasicInformationNotSavedList = basicInformationNotSavedList
                                    .stream()
                                    .filter(basicInformation -> outboundApplicationIdList
                                            .contains(basicInformation.getApplicationId()))
                                    .collect(Collectors.toList());
                            // 将 BasicInformation 和 BasicInformationNotSaved 合并成一个 Map<String, Object> 的
                            // List
                            List<Map<String, Object>> inspectionData = new ArrayList<>();
                            for (BasicInformation basicInformation : batchBasicInformationList) {
                                Map<String, Object> map = BeanUtil.beanToMap(basicInformation, false, true);
                                // 查找对应的 BasicInformationNotSaved 对象,并将其属性覆盖到 map 中
                                batchBasicInformationNotSavedList.stream()
                                        .filter(
                                                item -> item.getId().equals(basicInformation.getNotSubmittedRecordId()))
                                        .findFirst()
                                        .ifPresent(
                                                basicInformationNotSaved -> map.putAll(
                                                        BeanUtil.beanToMap(basicInformationNotSaved, false, true)));
                                // 匹配质检记录对应的出库传递单
                                /*
                                 * Optional<OutboundInformationTransferRecord> first =
                                 * transferRecordList.stream()
                                 * .filter(
                                 * record ->
                                 * basicInformation
                                 * .getApplicationId()
                                 * .equals(record.getApplicationId()))
                                 * .findFirst();
                                 * first.ifPresent(
                                 * outboundInformationTransferRecord ->
                                 * map.put(
                                 * "outboundQuantity",
                                 * outboundInformationTransferRecord.getQuantity()));
                                 */
                                List<OutboundInformationTransferRecord> recordList = entry.getValue().stream()
                                        .filter(record -> basicInformation
                                                .getApplicationId()
                                                .equals(record.getApplicationId()))
                                        .collect(Collectors.toList());
                                // 计算该 productionBatch 下所有 transferRecord 的 weight 总和
                                BigDecimal qualityWeight = recordList.stream()
                                        .map(OutboundInformationTransferRecord::getWeight)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal totalQualityQuantity = qualityWeight
                                        .multiply(new BigDecimal(1000))
                                        .divide(new BigDecimal(packagingNumbers), RoundingMode.HALF_UP);
                                map.put("outboundQuantity",
                                        totalQualityQuantity.stripTrailingZeros().toPlainString());
                                // 填充出库传递信息（生产部分）
                                map.put("production_productionBatch", transferRecord.getOutboundProductionBatch());
                                map.put("production_productName", transferRecord.getProductName());
                                map.put("production_totalWeight", transferRecord.getTotalWeight());
                                map.put("production_weight", transferRecord.getWeight());
                                map.put("production_quantity", transferRecord.getQuantity());
                                map.put("production_productionPackagingSpecification",
                                        transferRecord.getProductionPackagingSpecification());
                                inspectionData.add(map);
                            }
                            productionAndQualityDataDTO.setQualityInspectionData(inspectionData);
                            // 按照质检批号排序
                            inspectionData.sort(
                                    (map1, map2) -> {
                                        String batch1 = (String) map1.get("qualityInspectionBatch");
                                        String batch2 = (String) map2.get("qualityInspectionBatch");
                                        return batch1.compareTo(batch2);
                                    });
                            return productionAndQualityDataDTO;
                        })
                .collect(Collectors.toList());
        // 构建指标map
        Map<String, Map<String, String>> recordRequirementMap = getRequirementMap(transferRecordDTO.getRequirement());
        // 实时获取随车质检单基础信息
        getBaseInfo(transferRecordDTO.getCustomerNumber(), transferRecordDTO.getProductNumber(),
                transferRecordDTO.getProductCategoryNumber(), recordRequirementMap,
                transferRecordDTO.getRequirementCategory());
        recordRequirementMap.remove("分子式");
        recordRequirementMap.remove("分子量");
        recordRequirementMap.remove("规格依据");
        recordRequirementMap.remove("客户产品代码");
        // 去除隐藏的检测项目
        removeHiddenItems(
                recordRequirementMap,
                transferRecordDTO.getRequirementCategory(),
                transferRecordDTO.getCustomerNumber(),
                transferRecordDTO.getProductNumber(),
                transferRecordDTO.getProductCategoryNumber());

        // 查询并去重形成通用指标列表
        List<String> generalRequirementList = applicationMapper.selectList(
                new LambdaQueryWrapper<QualityInspectionApplication>()
                        .in(QualityInspectionApplication::getId, applicationIdList))
                .stream()
                .map(QualityInspectionApplication::getGeneralRequirement)
                .filter(StrUtil::isNotBlank) // 过滤空值
                .distinct() // 去重
                .collect(Collectors.toList());

        // 添加判断逻辑
        boolean groupRequirement = true;
        if (StrUtil.isNotBlank(transferRecordDTO.getRequirement())) {
            // 将requirement按分号分割成项目列表
            String[] currentRequirements = transferRecordDTO.getRequirement().split(";");

            // 遍历通用指标列表
            for (String generalRequirement : generalRequirementList) {
                if (StrUtil.isBlank(generalRequirement)) {
                    continue;
                }

                // 将通用指标按分号分割
                String[] generalItems = generalRequirement.split(";");

                // 计算匹配项数量
                int matchCount = 0;
                int totalItems = currentRequirements.length;

                // 比较每个检测项
                for (String currentItem : currentRequirements) {
                    String[] currentItemParts = currentItem.split(":");
                    String currentItemName = currentItemParts[0].split("\\|")[0];
                    String currentItemValue = currentItemParts.length > 1 ? currentItemParts[1] : "";

                    for (String generalItem : generalItems) {
                        if (StrUtil.isBlank(generalItem)) {
                            continue;
                        }
                        String[] generalItemParts = generalItem.split(":");
                        String generalItemName = generalItemParts[0].split("\\|")[0];
                        String generalItemValue = generalItemParts.length > 1 ? generalItemParts[1] : "";

                        // 如果项目名称和值都匹配
                        if (currentItemName.equals(generalItemName) && currentItemValue.equals(generalItemValue)) {
                            matchCount++;
                            break;
                        }
                    }
                }

                // 如果全部匹配，认为是通用要求
                if (matchCount == totalItems) {
                    groupRequirement = false;
                    break;
                }
            }
        }
        transferRecordDetailsDTO.setGroupRequirement(groupRequirement);
        // 不是集团指标
        if (!groupRequirement) {
            // 处理附加要求信息
            handleAdditionalRequirement(
                    recordRequirementMap,
                    transferRecordDTO.getCustomerNumber(),
                    transferRecordDTO.getProductNumber(),
                    transferRecordDTO.getProductCategoryNumber());
        }
        // 创建 requirementColumns
        List<Map<String, Object>> requirementColumns = recordRequirementMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> newMap = new HashMap<>();
                    newMap.put("label", entry.getKey());
                    newMap.put("matchingName", entry.getValue().get("matchingName"));
                    newMap.put("itemName", entry.getValue().get("itemName"));
                    return newMap;
                })
                .collect(Collectors.toList());
        // 创建 requirementData
        List<Map<String, Object>> requirementData = new ArrayList<>();

        Map<String, Object> requirementMap = recordRequirementMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get("requirement")));
        // MBTS初熔点特殊处理
        if (StrUtil.contains(transferRecordDTO.getProductName(), "MBTS")) {
            Company company = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCompanyNumber, transferRecordDTO.getCustomerNumber()));
            if (ObjUtil.isNull(company)) {
                requirementMap.put("初熔点", "≥166.0℃");
            } else {
                List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectList(
                        new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                .eq(
                                        GroupQualityStandard::getProductNumber,
                                        transferRecordDTO.getProductNumber()));
                if (CollUtil.isEmpty(groupQualityStandardList)) {
                    groupQualityStandardList = groupQualityStandardMapper.selectList(
                            new LambdaQueryWrapper<GroupQualityStandard>()
                                    .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                    .isNull(GroupQualityStandard::getProductNumber)
                                    .eq(
                                            GroupQualityStandard::getProductCategoryNumber,
                                            transferRecordDTO.getProductCategoryNumber()));
                }
                if (CollUtil.isEmpty(groupQualityStandardList)) {
                    requirementMap.put("初熔点", "≥166.0℃");
                }
            }
        }
        // 7020不溶性硫磺特殊处理
        if (StrUtil.contains(transferRecordDTO.getProductName(), "7020")) {
            Company company = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCompanyNumber, transferRecordDTO.getCustomerNumber()));
            if (ObjUtil.isNull(company)) {
                requirementMap.put("总硫含量", "79.0-81.0%");
                requirementMap.put("不溶性硫磺含量", "≥72.0%");
                requirementMap.put("灰分", "≤0.15%");
            } else {
                List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectList(
                        new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                .eq(
                                        GroupQualityStandard::getProductNumber,
                                        transferRecordDTO.getProductNumber()));
                if (CollUtil.isEmpty(groupQualityStandardList)) {
                    groupQualityStandardList = groupQualityStandardMapper.selectList(
                            new LambdaQueryWrapper<GroupQualityStandard>()
                                    .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                    .isNull(GroupQualityStandard::getProductNumber)
                                    .eq(
                                            GroupQualityStandard::getProductCategoryNumber,
                                            transferRecordDTO.getProductCategoryNumber()));
                }
                if (CollUtil.isEmpty(groupQualityStandardList)) {
                    requirementMap.put("总硫含量", "79.0-81.0%");
                    requirementMap.put("不溶性硫磺含量", "≥72.0%");
                    requirementMap.put("灰分", "≤0.15%");
                }
            }
        }
        requirementData.add(requirementMap);
        // 查询物料信息
        MaterialInformation materialInformation = materialInformationMapper.selectOne(
                new LambdaQueryWrapper<MaterialInformation>()
                        .eq(MaterialInformation::getMaterialNumber, transferRecordDTO.getProductNumber()));
        // 查询产品类别优先级
        ProductTypeForSort productTypeForSort = productTypeForSortMapper.selectOne(
                new LambdaQueryWrapper<ProductTypeForSort>()
                        .eq(
                                StrUtil.startWith(materialInformation.getCategoryNumber(), "0105"),
                                ProductTypeForSort::getProductCategoryNumber,
                                "0105")
                        .eq(
                                !StrUtil.startWith(materialInformation.getCategoryNumber(), "0105"),
                                ProductTypeForSort::getProductCategoryNumber,
                                materialInformation.getCategoryNumber()));
        // 查询检测项目优先级
        List<ItemPriorityForSort> itemPriorityForSorts = itemPriorityForSortMapper.selectList(
                new LambdaQueryWrapper<ItemPriorityForSort>()
                        .eq(ItemPriorityForSort::getProductTypeId, productTypeForSort.getId()));
        // 创建比较器
        Comparator<Map<String, Object>> comparator = (m1, m2) -> {
            String item1 = (String) m1.get("label");
            String item2 = (String) m2.get("label");
            // 查找 commonItemPriorityList 中的 priority
            int priority1 = getItemPriority(itemPriorityForSorts, item1);
            int priority2 = getItemPriority(itemPriorityForSorts, item2);
            // 如果 priority1 和 priority2 都不是 Integer.MAX_VALUE,则比较 priority
            if (priority1 != Integer.MAX_VALUE && priority2 != Integer.MAX_VALUE) {
                return Integer.compare(priority2, priority1);
            }
            // 如果 priority1 和 priority2 都是 Integer.MAX_VALUE,则按拼音排序
            else if (priority1 == Integer.MAX_VALUE && priority2 == Integer.MAX_VALUE) {
                return StrUtil.compare(PinyinUtil.getPinyin(item1), PinyinUtil.getPinyin(item2), true);
            }
            // 如果 priority1 不是 Integer.MAX_VALUE,priority2 是,则 item1 在前
            else if (priority1 != Integer.MAX_VALUE) {
                return -1;
            }
            // 如果 priority2 不是 Integer.MAX_VALUE,priority1 是,则 item2 在前
            else {
                return 1;
            }
        };
        // 根据 commonItemPriorityList 中 item 和 priority 属性对 requirementColumns 排序
        CollUtil.sort(requirementColumns, comparator);

        // 特殊处理：将"有效期"和"保质期"相关的项移到最后
        List<Map<String, Object>> expiryItems = new ArrayList<>();
        Iterator<Map<String, Object>> iterator = requirementColumns.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> item = iterator.next();
            Object labelObj = item.get("label");
            if (labelObj != null && (labelObj.toString().contains("有效期") || labelObj.toString().contains("保质期"))) {
                expiryItems.add(item);
                iterator.remove();
            }
        }
        // 将有效期和保质期相关项添加到列表末尾
        requirementColumns.addAll(expiryItems);

        // 填充检测数据
        recordRequirementMap.forEach(
                (key, value) -> {
                    // 获取Handler
                    QualityInspectionHandler qualityInspectionHandler = QualityInspectionHandlerFactory
                            .getInvokeStrategy(key.split(",")[0]);
                    // 编辑质检分析记录
                    if (qualityInspectionHandler != null) {
                        qualityInspectionHandler.outboundItemDataFillIn(
                                notSubmittedRecordIdList, productionAndQualityDataDTOList);
                    }
                });
        // 填充 requirementColumns 和 requirementData
        transferRecordDetailsDTO.setRequirementColumns(requirementColumns);

        transferRecordDetailsDTO.setRequirementData(requirementData);
        // 创建 qualityDataColumns
        Set<String> removeSet = new HashSet<>();
        removeSet.add("备注");
        // removeSet.add("有效期");
        // removeSet.add("保质期");
        List<Map<String, Object>> qualityDataColumns = new ArrayList<>();
        requirementColumns.forEach(
                item -> {
                    if (!removeSet.contains(item.get("label").toString())) {
                        qualityDataColumns.add(item);
                    }
                });
        // 填充 qualityDataColumns 和 productionAndQualityDataList
        transferRecordDetailsDTO.setQualityDataColumns(qualityDataColumns);
        // 排序 productionAndQualityDataDTOList
        productionAndQualityDataDTOList.sort(
                (dto1, dto2) -> {
                    if (dto1.getTransferRecordList() == null || dto1.getTransferRecordList().isEmpty()) {
                        return -1; // 处理空或空列表的情况
                    }
                    if (dto2.getTransferRecordList() == null || dto2.getTransferRecordList().isEmpty()) {
                        return 1; // 处理空或空列表的情况
                    }
                    String batch1 = dto1.getTransferRecordList().get(0).getOutboundProductionBatch();
                    String batch2 = dto2.getTransferRecordList().get(0).getOutboundProductionBatch();
                    return batch1.compareTo(batch2);
                });
        transferRecordDetailsDTO.setProductionAndQualityDataList(productionAndQualityDataDTOList);
        return transferRecordDetailsDTO;
    }

    @Override
    public TransportReportDTO creatTransportReport(TransportReportCreatDTO transportReportCreatDTO) {
        TransportReportDTO transportReportDTO = new TransportReportDTO();
        transportReportDTO.setTransferRecord(transportReportCreatDTO.getTransferRecord());
        // 获取备注
        Map<String, String> remarksMap = getRemarks(transportReportCreatDTO.getTransferRecord().getCustomerNumber());
        transportReportDTO.setGroupRemark(remarksMap.get("groupRemarks"));
        transportReportDTO.setCompanyRemark(remarksMap.get("companyRemarks"));

        TransportQualityInspectionReport transportReport = new TransportQualityInspectionReport();
        transportReport.setLinkId(transportReportCreatDTO.getTransferRecord().getLinkId());
        transportReport.setInspectionRequirement(
                transportReportCreatDTO.getTransferRecord().getRequirement());
        transportReport.setOutboundNumber(
                transportReportCreatDTO.getTransferRecord().getOutboundNumber());
        transportReport.setCustomerName(transportReportCreatDTO.getTransferRecord().getCustomerName());
        transportReport.setWeightInTons(
                transportReportCreatDTO.getTransferRecord().getTotalWeight().toPlainString());
        transportReport.setProductName(transportReportCreatDTO.getTransferRecord().getProductName());
        String productionPackagingSpecification = transportReportCreatDTO.getTransferRecord()
                .getProductionPackagingSpecification();
        if (NumberUtil.parseInt(productionPackagingSpecification) > 100) {
            productionPackagingSpecification += "/包";
        } else {
            productionPackagingSpecification += "/袋";
        }
        productionPackagingSpecification = productionPackagingSpecification.toUpperCase();
        transportReport.setPackagingSpecifications(productionPackagingSpecification);
        // 设置执行标准（质检记录）
        transportReport.setExecutionStandard(
                transportReportCreatDTO.getTransferRecord().getExecutionStandard());
        // 获取并设置执行标准（数据库）
        String executionStandards = getExecutionStandards(
                transportReportCreatDTO.getTransferRecord().getCustomerNumber(),
                transportReportCreatDTO.getTransferRecord().getProductNumber(),
                transportReportCreatDTO.getTransferRecord().getProductCategoryNumber());
        if (StrUtil.isNotBlank(executionStandards)) {
            transportReport.setExecutionStandard(executionStandards);
        }

        transportReport.setInspectionConclusion("合格");
        transportReport.setReportDate(transportReportDTO.getTransferRecord().getOutboundDate());

        Staff staff = staffMapper.selectOne(
                new LambdaQueryWrapper<Staff>()
                        .eq(Staff::getUserId, transportReportCreatDTO.getUser().getId()));
        transportReport.setAuditor(staff.getAuditor1());
        transportReport.setVerifier(staff.getAuditor2());

        // 更新执行标准和结论的逻辑
        updateExecutionStandardAndConclusion(
                transportReport,
                transportReportCreatDTO.getTransferRecord().getCustomerNumber(),
                transportReportCreatDTO.getTransferRecord().getProductCategoryNumber());

        transportReportDTO.setTransportQualityInspectionReport(transportReport);
        transportReportDTO.setRequirementColumns(transportReportCreatDTO.getRequirementColumns());
        transportReportDTO.setRequirementData(transportReportCreatDTO.getRequirementData());

        List<Map<String, Object>> qualityInspectionData = new ArrayList<>();
        Set<String> inspectorLabelSet = new HashSet<>();
        transportReportCreatDTO
                .getTransportReportCreatDataList()
                .forEach(
                        creatData -> {
                            // 生产批号为实际出库批号
                            String productionBatch = creatData.getOutboundProductionBatch();
                            String productionDateString;
                            creatData.getQualityData().put("productionBatch", productionBatch);
                            creatData.getQualityData().put("isHistory", creatData.getIsHistory());
                            creatData.getQualityData().put("quantity", creatData.getQuantity());
                            String dateStr;
                            int year;
                            int month;
                            int day;
                            LocalDate productionDate;
                            switch (productionBatch.length()) {
                                case 9:
                                    try {
                                        // 提取生产日期
                                        dateStr = productionBatch.substring(0, 6);
                                        // 年
                                        year = Integer.parseInt("20" + dateStr.substring(0, 2));
                                        // 月
                                        month = Integer.parseInt(dateStr.substring(2, 4));
                                        // 日
                                        day = Integer.parseInt(dateStr.substring(4, 6));
                                        productionDate = LocalDate.of(year, month, day);
                                        // 生产日期
                                        productionDateString = productionDate.toString();
                                    } catch (Exception e) {
                                        // 如果日期无效，将 productionDateString 设置为空字符串
                                        productionDateString = "";
                                    }
                                    break;
                                case 10:
                                    try {
                                        // 提取生产日期
                                        dateStr = productionBatch.substring(2, 8);
                                        // 年
                                        year = Integer.parseInt("20" + dateStr.substring(0, 2));
                                        // 月
                                        month = Integer.parseInt(dateStr.substring(2, 4));
                                        // 日
                                        day = Integer.parseInt(dateStr.substring(4, 6));
                                        productionDate = LocalDate.of(year, month, day);
                                        // 生产日期
                                        productionDateString = productionDate.toString();
                                    } catch (Exception e) {
                                        // 如果日期无效，将 productionDateString 设置为空字符串
                                        productionDateString = "";
                                    }
                                    break;
                                case 11:
                                    try {
                                        if (transportReportCreatDTO
                                                .getTransferRecord()
                                                .getProductCategoryNumber()
                                                .startsWith("0105")) {
                                            // 提取生产日期
                                            dateStr = productionBatch.substring(2, 8);
                                        } else {
                                            // 提取生产日期
                                            dateStr = productionBatch.substring(3, 9);
                                        }
                                        // 年
                                        year = Integer.parseInt("20" + dateStr.substring(0, 2));
                                        // 月
                                        month = Integer.parseInt(dateStr.substring(2, 4));
                                        // 日
                                        day = Integer.parseInt(dateStr.substring(4, 6));
                                        // 生产日期
                                        productionDate = LocalDate.of(year, month, day);
                                        productionDateString = productionDate.toString();
                                    } catch (Exception e) {
                                        // 如果日期无效，将 productionDateString 设置为空字符串
                                        productionDateString = "";
                                    }
                                    break;
                                case 13:
                                    try {
                                        // 提取生产日期
                                        dateStr = productionBatch.substring(3, 9);
                                        // 年
                                        year = Integer.parseInt("20" + dateStr.substring(0, 2));
                                        // 月
                                        month = Integer.parseInt(dateStr.substring(2, 4));
                                        // 日
                                        day = Integer.parseInt(dateStr.substring(4, 6));
                                        productionDate = LocalDate.of(year, month, day);
                                        // 生产日期
                                        productionDateString = productionDate.toString();
                                    } catch (Exception e) {
                                        // 如果日期无效，将 productionDateString 设置为空字符串
                                        productionDateString = "";
                                    }
                                    break;
                                default:
                                    productionDateString = "";
                            }
                            creatData.getQualityData().put("productionDate", productionDateString);
                            qualityInspectionData.add(creatData.getQualityData());

                            inspectorLabelSet.add(creatData.getQualityData().get("auditor1").toString());
                        });
        List<SelectLabelDTO> inspectorLabelList = new ArrayList<>();
        inspectorLabelSet.forEach(
                inspector -> {
                    SelectLabelDTO selectLabelDTO = new SelectLabelDTO();
                    selectLabelDTO.setLabel(inspector);
                    selectLabelDTO.setValue(inspector);
                    inspectorLabelList.add(selectLabelDTO);
                });
        transportReportDTO.setInspectorLabelList(inspectorLabelList);
        qualityInspectionData.sort(
                (map1, map2) -> {
                    String batch1 = (String) map1.get("productionBatch");
                    String batch2 = (String) map2.get("productionBatch");
                    return batch1.compareTo(batch2);
                });
        transportReportDTO.setQualityInspectionData(qualityInspectionData);
        return transportReportDTO;
    }

    @Override
    public Boolean saveTransportReport(TransportReportDTO transportReportDTO) {
        TransferRecordDTO transferRecord = transportReportDTO.getTransferRecord();

        TransportQualityInspectionReport transportQualityInspectionReport = transportReportDTO
                .getTransportQualityInspectionReport();
        List<Map<String, Object>> requirementColumns = transportReportDTO.getRequirementColumns();
        Map<String, Object> requirementData = transportReportDTO.getRequirementData().get(0);

        // 查询质检项目对应关系
        List<InspectionItemReference> inspectionItemReferenceList = inspectionItemReferenceMapper.selectList(null);
        // 转换为map
        Map<String, String> inspectionItemReferenceMap = inspectionItemReferenceList.stream()
                .collect(
                        Collectors.toMap(
                                InspectionItemReference::getMatchingName,
                                InspectionItemReference::getItemName));
        StringBuilder inspectionRequirementBuilder = new StringBuilder();
        requirementColumns.forEach(
                requirementMap -> {
                    // 检查 requirementMap 是否为 null
                    if (requirementMap != null) {
                        String itemName = requirementMap.get("itemName").toString();
                        String matchingName = requirementMap.get("matchingName").toString();
                        String requirement = requirementData.get(matchingName).toString();
                        // 醇类不溶物指标特殊处理
                        if (StrUtil.contains(requirement, "甲醇")) {
                            requirement = StrUtil.split(requirement, ",").get(0);
                            itemName = "甲醇不溶物";
                        }
                        if (StrUtil.contains(requirement, "乙醇")) {
                            requirement = StrUtil.split(requirement, ",").get(0);
                            itemName = "乙醇不溶物";
                        }
                        if (StrUtil.contains(requirement, "丙醇")) {
                            requirement = StrUtil.split(requirement, ",").get(0);
                            itemName = "丙醇不溶物";
                        }
                        // 其他指标特殊处理
                        if (StrUtil.contains(requirement, "占总硫") || StrUtil.contains(requirement, "去除")) {
                            requirement = StrUtil.split(requirement, ",").get(0);
                        }
                        for (Map.Entry<String, String> entry : inspectionItemReferenceMap.entrySet()) {
                            String key = entry.getKey();
                            String value = entry.getValue();
                            if (StrUtil.contains(itemName, key)) {
                                itemName = StrUtil.replace(itemName, key, value);
                            }
                        }
                        inspectionRequirementBuilder
                                .append(itemName)
                                .append(":")
                                .append(requirement)
                                .append(";");
                    }
                });
        transportQualityInspectionReport.setInspectionRequirement(
                inspectionRequirementBuilder.toString());
        if (ObjUtil.isNull(transportQualityInspectionReport.getId())) {
            transportQualityInspectionReportMapper.insert(transportQualityInspectionReport);
        } else {
            transportQualityInspectionReportMapper.updateById(transportQualityInspectionReport);
        }
        transportQualityInspectionDataMapper.delete(
                new LambdaQueryWrapper<TransportQualityInspectionData>()
                        .eq(
                                TransportQualityInspectionData::getReportId,
                                transportQualityInspectionReport.getId()));
        transportReportDTO
                .getQualityInspectionData()
                .forEach(
                        inspectionData -> {
                            TransportQualityInspectionData transportQualityInspectionData = new TransportQualityInspectionData();
                            transportQualityInspectionData.setReportId(transportQualityInspectionReport.getId());
                            transportQualityInspectionData.setProductionBatch(
                                    inspectionData.get("productionBatch").toString());
                            transportQualityInspectionData.setProductionDate(
                                    inspectionData.get("productionDate").toString());
                            transportQualityInspectionData.setNumberOfPackages(
                                    inspectionData.get("quantity").toString());
                            transportQualityInspectionData.setRawInspectionData(inspectionData);
                            StringBuilder inspectionDataBuilder = new StringBuilder();
                            /*
                             * sourceRequirementMap.forEach((key, value) -> {
                             * inspectionDataBuilder.append(sourceRequirementMap.get(key).get("itemName")).
                             * append(":").append(inspectionData.get(key)).append(";");
                             * });
                             */
                            requirementColumns.forEach(
                                    requirementMap -> {
                                        // 检查 requirementMap 是否为 null
                                        if (requirementMap != null) {
                                            String itemName = requirementMap.get("itemName").toString();
                                            String matchingName = requirementMap.get("matchingName").toString();
                                            String requirement = requirementData.get(matchingName).toString();
                                            Object result = inspectionData.get(matchingName);
                                            // 醇类不溶物指标特殊处理
                                            if (StrUtil.contains(requirement, "甲醇")) {
                                                itemName = "甲醇不溶物";
                                            }
                                            if (StrUtil.contains(requirement, "乙醇")) {
                                                itemName = "乙醇不溶物";
                                            }
                                            if (StrUtil.contains(requirement, "丙醇")) {
                                                itemName = "丙醇不溶物";
                                            }
                                            // 其他指标特殊处理
                                            for (Map.Entry<String, String> entry : inspectionItemReferenceMap
                                                    .entrySet()) {
                                                String key = entry.getKey();
                                                String value = entry.getValue();
                                                if (StrUtil.contains(itemName, key)) {
                                                    itemName = StrUtil.replace(itemName, key, value);
                                                }
                                            }
                                            inspectionDataBuilder.append(itemName).append(":").append(result)
                                                    .append(";");
                                        }
                                    });
                            transportQualityInspectionData.setInspectionData(inspectionDataBuilder.toString());
                            transportQualityInspectionDataMapper.insert(transportQualityInspectionData);
                        });

        List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transferRecord.getLinkId())
                        .eq(
                                OutboundInformationTransferRecord::getOutboundNumber,
                                transferRecord.getOutboundNumber())
                        .eq(
                                OutboundInformationTransferRecord::getProductName,
                                transferRecord.getProductName()));
        transferRecordList.forEach(
                transferRecordItem -> {
                    transferRecordItem.setHandleStatus(true);
                    transferRecordMapper.updateById(transferRecordItem);
                });
        return true;
    }

    @Override
    public List<String> creatTransportReportPicture(Integer linkId, String outboundNumber, String productName) {
        List<String> urlList = new ArrayList<>();
        // 查询质检报告
        TransportQualityInspectionReport transportReport = transportQualityInspectionReportMapper.selectOne(
                new LambdaQueryWrapper<TransportQualityInspectionReport>()
                        .eq(TransportQualityInspectionReport::getLinkId, linkId)
                        .eq(TransportQualityInspectionReport::getOutboundNumber, outboundNumber)
                        .eq(TransportQualityInspectionReport::getProductName, productName));
        // 查询出库传递单
        OutboundInformationTransferRecord transferRecord = transferRecordMapper.selectOne(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transportReport.getLinkId())
                        .eq(OutboundInformationTransferRecord::getOutboundNumber,
                                transportReport.getOutboundNumber())
                        .eq(OutboundInformationTransferRecord::getProductName,
                                transportReport.getProductName())
                        .groupBy(OutboundInformationTransferRecord::getOutboundNumber));
        // 处理出库传递单的对方合同号（如果对方合同号明细不为空且不包含汉字，则使用对方合同号明细作为对方合同号）
        if (StrUtil.isNotBlank(transferRecord.getTheirContractDetail())
                && !ReUtil.contains("[\\u4e00-\\u9fa5]", transferRecord.getTheirContractDetail())) {
            transferRecord.setTheirContractNumber(transferRecord.getTheirContractDetail());
        }

        // 查询质检数据
        List<TransportQualityInspectionData> transportReportDataList = transportQualityInspectionDataMapper.selectList(
                new LambdaQueryWrapper<TransportQualityInspectionData>()
                        .eq(TransportQualityInspectionData::getReportId, transportReport.getId()));

        // 获取订单原始质检要求
        Map<String, Map<String, String>> saleOrderRequirementMap = getRequirementMap(transferRecord.getRequirement());
        // 实时获取随车质检单基础信息
        getBaseInfo(transferRecord.getCustomerNumber(), transferRecord.getProductNumber(),
                transferRecord.getProductCategoryNumber(), saleOrderRequirementMap,
                transferRecord.getRequirementCategory());

        // 查询检测方法
        List<TransportReportItemTestMethod> testMethodList = getTestMethodsByCustomerNumber(
                transferRecord.getCustomerNumber(),
                transferRecord.getProductCategoryNumber());
        // 查询随车质检单设置
        Map<String, Boolean> configMap = getCustomerReportConfig(transferRecord.getCustomerNumber());

        // 获取质检要求
        List<String> requirementList = CollUtil.toList(transportReport.getInspectionRequirement().split(";"));
        // 根据设置添加检测日期和罐次
        // 检查罐次
        if (Boolean.TRUE.equals(configMap.get("isAddTankNumber"))) {
            requirementList.add(0, "罐次:1;");
        }
        // 检测日期 (后添加的会在前面)
        if (Boolean.TRUE.equals(configMap.get("isAddInspectionDate"))) {
            requirementList.add(0, "检测日期:1;");
        }

        // 查询模板
        List<TransportReportTemplate> transportReportTemplateList = getTransportReportTemplates(
                transportReportDataList.size(), requirementList.size(),
                CollUtil.isNotEmpty(testMethodList), transferRecord.getCustomerNumber());
        // 查询翻译
        Map<String, Map<String, String>> i18nMap = getI18nMapByCustomerNumber(transferRecord.getCustomerNumber());
        // 模板排序
        transportReportTemplateList.sort(Comparator.comparingInt(TransportReportTemplate::getPriority));

        String commonBaseNamePart = FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + "_"
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + "_"
                + transportReport.getOutboundNumber()
                + "_"
                + transportReport.getId();

        String directoryInfo = filePath
                + FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + File.separator
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + File.separator;

        transportReportTemplateList.forEach(transportReportTemplate -> {
            // 判断是否为竖版模板
            boolean isVerticalTemplate = transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2;
            
            // 根据模板语言类型获取对应的配置
            String languageConfigKey = "isSeparateReportPerBatch_" + transportReportTemplate.getLanguageType();
            boolean isSeparateByLanguage = Boolean.TRUE.equals(configMap.get(languageConfigKey));
            
            // 竖版模板强制生成多个文件，或者根据语言类型配置判断是否生成多个文件
            boolean generateMultipleFiles = (isVerticalTemplate || isSeparateByLanguage) 
                    && transportReportDataList.size() > 1;

            List<List<TransportQualityInspectionData>> reportDataSets = new ArrayList<>();
            List<String> reportFileBaseNames = new ArrayList<>();

            if (generateMultipleFiles) {
                for (TransportQualityInspectionData batchData : transportReportDataList) {
                    reportDataSets.add(Collections.singletonList(batchData));
                    String batchNumberForFilename = FileNameUtil.cleanInvalid(batchData.getProductionBatch());
                    reportFileBaseNames.add(commonBaseNamePart + "_" + batchNumberForFilename);
                }
            } else {
                reportDataSets.add(transportReportDataList);
                reportFileBaseNames.add(commonBaseNamePart);
            }

            for (int reportIndex = 0; reportIndex < reportDataSets.size(); reportIndex++) {
                List<TransportQualityInspectionData> currentReportDataSet = reportDataSets.get(reportIndex);
                String currentReportFileBaseName = reportFileBaseNames.get(reportIndex);

                Workbook workbook = new Workbook();
                // 加载文件
                workbook.loadFromFile(transportReportTemplate.getPath());
                // 获取工作表
                Worksheet sheet = workbook.getWorksheets().get(0);

                // 处理模板字段（包含客户排除字段）
                Set<String> templateFieldSet = processTemplateFields(transportReportTemplate, transferRecord);

                // 处理客户自定义字段内容
                List<String> customerItemList = processCustomerFields(
                        sheet,
                        transportReportTemplate,
                        saleOrderRequirementMap,
                        configMap, // 直接传递原始configMap，允许修改isAddCustomerProductCode标志
                        templateFieldSet,
                        transferRecord,
                        transportReport,
                        i18nMap);

                // 客户名称
                if (templateFieldSet.contains("客户名称")) {
                    CellRange customerName = sheet.getCellRange(transportReportTemplate.getCustomerNameRow(),
                            transportReportTemplate.getCustomerNameColumn());
                    customerName.setText(transportReport.getCustomerName());
                    // 客户名称_英文通用模板特殊处理
                    if (transportReportTemplate.getLanguageType() == 2) {
                        String contractNumber = transferRecord.getOurContractNumber();
                        if (StrUtil.isNotBlank(contractNumber)) {
                            try {
                                // 获取当前年份的后两位
                                int currentYearLastTwoDigits = Year.now().getValue() % 100;

                                // 检查合同号长度是否足够
                                if (contractNumber.length() >= 4) {
                                    // 获取前两位
                                    String yearPart = contractNumber.substring(0, 2);
                                    // 获取第三四位
                                    String suffix = contractNumber.substring(2, 4).toUpperCase();

                                    // 将年份部分转换为整数
                                    int yearNum = Integer.parseInt(yearPart);

                                    // 检查年份范围：当前年份-1、当前年份、当前年份+1
                                    boolean isYearInRange = (yearNum == currentYearLastTwoDigits - 1) ||
                                            (yearNum == currentYearLastTwoDigits) ||
                                            (yearNum == currentYearLastTwoDigits + 1);

                                    // 检查格式
                                    if (isYearInRange && suffix.equals("SX")) {
                                        // 替换SX为SXV
                                        contractNumber = contractNumber.substring(0, 2) + "SXV" +
                                                contractNumber.substring(4);
                                    }
                                }
                            } catch (Exception e) {
                                // 发生异常时，记录日志并保持contractNumber不变
                                log.error("处理合同号时发生异常：{}", e.getMessage(), e);
                                // 重新获取原始合同号，确保不会被错误修改
                                contractNumber = transferRecord.getOurContractNumber();
                            }
                        }
                        customerName.setText(contractNumber);
                    }
                }

                // 数量（吨）
                if (templateFieldSet.contains("数量")) {
                    CellRange weight = sheet.getCellRange(transportReportTemplate.getWeightRow(),
                            transportReportTemplate.getWeightColumn());
                    String weightText;
                    
                    // 当生成多个文件时，使用当前批次的重量
                    if (generateMultipleFiles && currentReportDataSet.size() == 1) {
                        // 根据当前批次的袋数和规格计算重量
                        TransportQualityInspectionData currentBatchData = currentReportDataSet.get(0);
                        String batchWeight;
                        
                        // 获取当前批次的袋数
                        String numberOfPackagesStr = currentBatchData.getNumberOfPackages();
                        // 获取产品规格
                        String packagingSpec = transportReport.getPackagingSpecifications();
                        
                        try {
                            if (StrUtil.isNotBlank(numberOfPackagesStr) && StrUtil.isNotBlank(packagingSpec)) {
                                // 解析袋数
                                int numberOfPackages = Integer.parseInt(numberOfPackagesStr);
                                // 从规格字符串中提取数字（例如："25KG" -> 25）
                                String specNumStr = packagingSpec.replaceAll("[^0-9.]", "");
                                if (StrUtil.isNotBlank(specNumStr)) {
                                    BigDecimal specWeight = new BigDecimal(specNumStr);
                                    // 计算总重量（袋数 × 单袋重量，转换为吨）
                                    BigDecimal totalWeight = BigDecimal.valueOf(numberOfPackages)
                                            .multiply(specWeight)
                                            .divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP);
                                    batchWeight = totalWeight.stripTrailingZeros().toPlainString();
                                } else {
                                    // 规格解析失败，使用总重量
                                    batchWeight = transportReport.getWeightInTons();
                                }
                            } else {
                                // 袋数或规格为空，使用总重量
                                batchWeight = transportReport.getWeightInTons();
                            }
                        } catch (NumberFormatException e) {
                            // 数字解析出错，使用总重量作为备用
                            log.warn("解析袋数或规格时发生数字格式异常，袋数: {}, 规格: {}, 使用总重量作为备用: {}", 
                                    numberOfPackagesStr, packagingSpec, e.getMessage());
                            batchWeight = transportReport.getWeightInTons();
                        } catch (Exception e) {
                            // 其他异常，使用总重量作为备用
                            log.warn("计算批次重量时发生异常，使用总重量作为备用: {}", e.getMessage());
                            batchWeight = transportReport.getWeightInTons();
                        }
                        
                        weightText = batchWeight + " 吨";
                        if (transportReportTemplate.getLanguageType() == 2) {
                            weightText = batchWeight + " MT";
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            weightText += "\n" + batchWeight + " T";
                        }
                    } else {
                        // 使用总重量（原有逻辑）
                        weightText = transportReport.getWeightInTons() + " 吨";
                        if (transportReportTemplate.getLanguageType() == 2) {
                            weightText = transportReport.getWeightInTons() + " MT";
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            weightText += "\n" + transportReport.getWeightInTons() + " T";
                        }
                    }
                    weight.setText(weightText);
                }

                // 产品名称
                if (templateFieldSet.contains("产品名称")) {
                    CellRange productType = sheet.getCellRange(transportReportTemplate.getProductTypeRow(),
                            transportReportTemplate.getProductTypeColumn());
                    String productTypeText = transportReport.getProductName()
                            .replace("国产", "")
                            .replace("(进料加工)", "")
                            .replace("（进料加工）", "");
                    if (transportReportTemplate.getLanguageType() == 1) {
                        if (i18nMap.containsKey(productTypeText)) {
                            if (StrUtil.isNotBlank(i18nMap.get(productTypeText).get("zhCnOptimized"))) {
                                productTypeText = i18nMap.get(productTypeText).get("zhCnOptimized");
                            }
                        }
                        if (configMap.get("isAddCustomerProductCode") &&
                                saleOrderRequirementMap.containsKey("客户产品代码")) {
                            productTypeText += " [" + saleOrderRequirementMap.get("客户产品代码").get("requirement") + "]";
                        }
                    }
                    if (transportReportTemplate.getLanguageType() == 2) {
                        if (i18nMap.containsKey(productTypeText)) {
                            productTypeText = i18nMap.get(productTypeText).get("enUs");
                        }
                        if (configMap.get("isAddCustomerProductCode") &&
                                saleOrderRequirementMap.containsKey("客户产品代码")) {
                            productTypeText += " [" + saleOrderRequirementMap.get("客户产品代码").get("requirement") + "]";
                        }
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        if (i18nMap.containsKey(productTypeText)) {
                            if (StrUtil.isNotBlank(i18nMap.get(productTypeText).get("zhCnOptimized"))) {
                                // 先获取英文翻译
                                String enUsText = i18nMap.get(productTypeText).get("enUs");
                                // 再处理中文优化文本
                                productTypeText = i18nMap.get(productTypeText).get("zhCnOptimized");
                                // 添加客户产品代码（如果需要）
                                if (configMap.get("isAddCustomerProductCode") &&
                                        saleOrderRequirementMap.containsKey("客户产品代码")) {
                                    productTypeText += " [" + saleOrderRequirementMap.get("客户产品代码").get("requirement")
                                            + "]";
                                }
                                // 添加英文翻译
                                productTypeText += "\n" + enUsText;
                            } else {
                                // 先获取英文翻译
                                String enUsText = i18nMap.get(productTypeText).get("enUs");
                                // 添加客户产品代码（如果需要）
                                if (configMap.get("isAddCustomerProductCode") &&
                                        saleOrderRequirementMap.containsKey("客户产品代码")) {
                                    productTypeText += " [" + saleOrderRequirementMap.get("客户产品代码").get("requirement")
                                            + "]";
                                }
                                // 添加英文翻译
                                productTypeText += "\n" + enUsText;
                            }
                        }
                    }
                    productType.setText(productTypeText);
                }

                // 包装规格
                if (templateFieldSet.contains("规格")) {
                    CellRange packagingSpecification = sheet.getCellRange(
                            transportReportTemplate.getPackingSpecificationRow(),
                            transportReportTemplate.getPackingSpecificationColumn());
                    String packagingSpecificationText = transportReport.getPackagingSpecifications();
                    if (transportReportTemplate.getLanguageType() == 2) {
                        packagingSpecificationText = packagingSpecificationText
                                .replace("袋", "BAG")
                                .replace("包", "CTN")
                                .replace("kg", "KG");
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        packagingSpecificationText += "\n" + packagingSpecificationText
                                .replace("袋", "Bag")
                                .replace("包", "Package")
                                .replace("kg", "KG");
                    }
                    packagingSpecification.setText(packagingSpecificationText);
                }

                // 执行标准
                if (templateFieldSet.contains("执行标准")) {
                    CellRange executionStandard = sheet.getCellRange(
                            transportReportTemplate.getExecutionStandardRow(),
                            transportReportTemplate.getExecutionStandardColumn());
                    String executionStandardText = transportReport.getExecutionStandard();
                    if (transportReportTemplate.getLanguageType() == 2) {
                        if (StrUtil.endWith(executionStandardText, "标准")) {
                            executionStandardText = "Customer Standard";
                        }
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        if (StrUtil.endWith(executionStandardText, "标准")) {
                            String standardText = executionStandardText.replace("标准", " Standard");
                            executionStandardText = executionStandardText + "\n" + standardText;
                        }
                    }
                    executionStandard.setText(executionStandardText);
                }

                // 检测指标
                // 创建requirementList的副本进行处理
                List<String> processedRequirementList = new ArrayList<>(requirementList);
                // 移除customerItemList中已存在的项目
                if (CollUtil.isNotEmpty(customerItemList)) {
                    processedRequirementList.removeIf(requirement -> {
                        String itemName = requirement.split(":")[0];
                        return customerItemList.contains(itemName);
                    });
                }
                // 遍历质检项目集合
                for (int i = 0; i < processedRequirementList.size(); i++) {
                    String itemString = processedRequirementList.get(i).split(":")[0];
                    String requirementString = processedRequirementList.get(i).split(":")[1];
                    // 依据 itemDirection 选择排列方式
                    CellRange item, itemI18n = null, requirement, testMethod = null;
                    int itemRow, itemCol, reqRow, reqCol;
                    if (transportReportTemplate.getItemDirection() != null
                            && transportReportTemplate.getItemDirection() == 2) {
                        // 竖向排列
                        itemRow = transportReportTemplate.getRequirementRow() + i;
                        itemCol = transportReportTemplate.getRequirementColumn();
                        if (transportReportTemplate.getLanguageType() == 3) {
                            // 中英文模板竖向排列时，在同一单元格内换行显示英文
                            itemI18n = null;
                            reqRow = itemRow;
                            reqCol = itemCol + 2; // Adjusted from itemCol + 1 as per existing code
                        } else {
                            reqRow = itemRow;
                            reqCol = itemCol + 2; // Adjusted from itemCol + 1
                        }
                        if (transportReportTemplate.getIsAddTestMethod()) {
                            testMethod = sheet.getCellRange(
                                    transportReportTemplate.getTestMethodRow() + i,
                                    transportReportTemplate.getTestMethodColumn());
                        }
                    } else {
                        // 横向排列（默认）
                        itemRow = transportReportTemplate.getRequirementRow();
                        itemCol = transportReportTemplate.getRequirementColumn() + i;
                        if (transportReportTemplate.getLanguageType() == 3) {
                            itemI18n = sheet.getCellRange(itemRow + 1, itemCol);
                            reqRow = itemRow + 2;
                            reqCol = itemCol;
                        } else {
                            reqRow = itemRow + 1;
                            reqCol = itemCol;
                        }
                        if (transportReportTemplate.getIsAddTestMethod()) {
                            testMethod = sheet.getCellRange(
                                    transportReportTemplate.getTestMethodRow(),
                                    transportReportTemplate.getTestMethodColumn() + i);
                        }
                    }
                    // 新增多语言 i18n 处理
                    if (ObjUtil.isNotNull(itemI18n)) {
                        if (i18nMap.containsKey(itemString)) {
                            itemI18n.setText(i18nMap.get(itemString).get("enUs"));
                        }
                        if (i18nMap.containsKey(requirementString)) {
                            requirementString += "\n" + i18nMap.get(requirementString).get("enUs");
                        }
                    }
                    item = sheet.getCellRange(itemRow, itemCol);
                    requirement = sheet.getCellRange(reqRow, reqCol);
                    if (testMethod != null) {
                        String testMethodText = getTestMethodForItem(itemString, testMethodList, saleOrderRequirementMap);
                        testMethod.setText(testMethodText);
                    }
                    // 竖向排列且为中英文模板时，将英文与中文在同一单元格内换行显示
                    String cellItemText = itemString;
                    String cellRequirementText = requirementString;
                    if (transportReportTemplate.getItemDirection() != null
                            && transportReportTemplate.getItemDirection() == 2
                            && transportReportTemplate.getLanguageType() == 3) {
                        if (i18nMap.containsKey(itemString)) {
                            cellItemText = itemString + "\n" + i18nMap.get(itemString).get("enUs");
                        }
                        if (i18nMap.containsKey(requirementString)) {
                            // Requirement string might already have \n for bilingual from previous block
                            // This logic might need review if requirementString can be an i18n key itself
                            String[] parts = requirementString.split("\\\n");
                            String baseReq = parts[0];
                            String enReq = parts.length > 1 ? parts[1] : null;
                            if(i18nMap.containsKey(baseReq) && StrUtil.isBlank(enReq)){
                                cellRequirementText = baseReq + "\n" + i18nMap.get(baseReq).get("enUs");
                            } else {
                                // Assuming requirementString is already final or not a key
                                 cellRequirementText = requirementString; // Keep as is if already bilingual
                            }
                        }
                    }
                    item.setText(cellItemText);
                    requirement.setText(cellRequirementText);
                    if (transportReportTemplate.getLanguageType() == 2) {
                        if (i18nMap.containsKey(itemString)) {
                            item.setText(i18nMap.get(itemString).get("enUs"));
                        }
                        if (i18nMap.containsKey(requirementString)) {
                             String[] parts = requirementString.split("\\\n");
                             String baseReq = parts[0];
                             requirement.setText(i18nMap.containsKey(baseReq) ? i18nMap.get(baseReq).get("enUs") : baseReq);
                        }
                    }

                    // 检测日期和罐次的特殊处理
                    if ("检测日期".equals(itemString) || "罐次".equals(itemString)) {
                        // 合并item至requirement
                        CellRange mergeRange = sheet.getCellRange(item.getRow(), item.getColumn(),
                                requirement.getRow(), item.getColumn()); // Use item.getColumn for merge end col
                        mergeRange.merge();

                        // 获取当前行第一个单元格作为参考
                        CellRange firstCellInRow = sheet.getCellRange(item.getRow(), 1);

                        // 设置item单元格样式
                        mergeRange.getCellStyle().setHorizontalAlignment(HorizontalAlignType.Center);
                        mergeRange.getCellStyle().setVerticalAlignment(VerticalAlignType.Center);
                        // 复制字体大小
                        mergeRange.getCellStyle().getExcelFont()
                                .setSize(firstCellInRow.getCellStyle().getExcelFont().getSize());

                        if (transportReportTemplate.getLanguageType() == 3) {
                            if ("检测日期".equals(itemString)) {
                                mergeRange.setText("检测日期\n\nInspection\n\nDate");
                            }
                            if ("罐次".equals(itemString)) {
                                mergeRange.setText("罐次\n\nTank\n\nBatch");
                            }
                        }
                    }
                }

                // 包装规格2
                if (templateFieldSet.contains("包装规格")) {
                    CellRange packagingSpecification2 = sheet.getCellRange(
                            transportReportTemplate.getPackingSpecificationRow2(),
                            transportReportTemplate.getPackingSpecificationColumn2());
                    if (transportReport.getPackagingSpecifications().contains("袋")) {
                        packagingSpecification2.setText("（袋）");
                        if (transportReportTemplate.getLanguageType() == 2) {
                            packagingSpecification2.setText("（BAGS）");
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            packagingSpecification2.setText("（袋/Bag）");
                        }
                    } else {
                        packagingSpecification2.setText("（包）");
                        if (transportReportTemplate.getLanguageType() == 2) {
                            packagingSpecification2.setText("（CTNS）");
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            packagingSpecification2.setText("（包/Package）");
                        }
                    }
                }
                
                // 遍历出库关联记录集合
                for (int i = 0; i < currentReportDataSet.size(); i++) {
                    TransportQualityInspectionData transportReportData = currentReportDataSet.get(i);
                    // 填充生产批号
                    CellRange productionBatchCell = sheet.getCellRange(transportReportTemplate.getProductionBatchRow() + i,
                            transportReportTemplate.getProductionBatchColumn());
                    productionBatchCell.setText(transportReportData.getProductionBatch());
                    // 填充生产日期
                    CellRange productionDateCell = sheet.getCellRange(transportReportTemplate.getProductionDateRow() + i,
                            transportReportTemplate.getProductionDateColumn());
                    productionDateCell.setText(transportReportData.getProductionDate());

                    List<String> dataList = new ArrayList<>(
                            Arrays.asList(transportReportData.getInspectionData().split(";")));

                    // 根据设置添加检测日期和罐次信息
                    int inspectionDataItemIndex = 0;
                    // 检测日期
                    if (Boolean.TRUE.equals(configMap.get("isAddInspectionDate"))) {
                        CellRange configData = sheet.getCellRange(transportReportTemplate.getDataRow() + i,
                                transportReportTemplate.getDataColumn() + inspectionDataItemIndex);
                        if (transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2) { 
                             configData = sheet.getCellRange(transportReportTemplate.getDataRow() + inspectionDataItemIndex,
                                transportReportTemplate.getDataColumn() + i);
                        } else {
                             configData = sheet.getCellRange(transportReportTemplate.getDataRow() + i,
                                transportReportTemplate.getDataColumn() + inspectionDataItemIndex);
                        }

                        configData.setText(transportReportData.getProductionDate());

                        CellRange firstCellInRowForData = sheet.getCellRange(configData.getRow(), 1);
                        configData.getCellStyle().setHorizontalAlignment(
                                firstCellInRowForData.getCellStyle().getHorizontalAlignment());
                        configData.getCellStyle().setVerticalAlignment(
                                firstCellInRowForData.getCellStyle().getVerticalAlignment());
                        configData.getCellStyle().getExcelFont()
                                .setSize(firstCellInRowForData.getCellStyle().getExcelFont().getSize());
                        
                        inspectionDataItemIndex++;
                    }
                    // 检查罐次
                    if (Boolean.TRUE.equals(configMap.get("isAddTankNumber"))) {
                         CellRange configData;
                         if (transportReportTemplate.getItemDirection() != null && transportReportTemplate.getItemDirection() == 2) { // Vertical items
                             configData = sheet.getCellRange(transportReportTemplate.getDataRow() + inspectionDataItemIndex,
                                transportReportTemplate.getDataColumn() + i);
                        } else { // Horizontal items
                             configData = sheet.getCellRange(transportReportTemplate.getDataRow() + i,
                                transportReportTemplate.getDataColumn() + inspectionDataItemIndex);
                        }
                        configData.setText(transportReportData.getRawInspectionData().get("tankBatch").toString());

                        CellRange firstCellInRowForData = sheet.getCellRange(configData.getRow(), 1);
                        configData.getCellStyle().setHorizontalAlignment(
                                firstCellInRowForData.getCellStyle().getHorizontalAlignment());
                        configData.getCellStyle().setVerticalAlignment(
                                firstCellInRowForData.getCellStyle().getVerticalAlignment());
                        configData.getCellStyle().getExcelFont()
                                .setSize(firstCellInRowForData.getCellStyle().getExcelFont().getSize());

                        inspectionDataItemIndex++;
                    }

                    // 移除customerItemList中已存在的项目
                    if (CollUtil.isNotEmpty(customerItemList)) {
                        dataList.removeIf(data -> {
                            String itemName = data.split(":")[0];
                            return customerItemList.contains(itemName);
                        });
                    }
                    // 遍历质检数据集合
                    for (int j = 0; j < dataList.size(); j++) {
                        // 填充质检数据
                        CellRange data;
                        if (transportReportTemplate.getItemDirection() != null &&
                                transportReportTemplate.getItemDirection() == 2) {
                            // 竖向排列：行由 inspectionDataItemIndex (项目索引)决定，列由 i (批次索引)决定
                            data = sheet.getCellRange(transportReportTemplate.getDataRow() + inspectionDataItemIndex, // Row uses combined item index
                                    transportReportTemplate.getDataColumn() + i); // Column is batch-indexed (i from outer batch loop)
                        } else {
                            // 横向排列（原逻辑）：行由 i (批次索引)决定，列由 inspectionDataItemIndex (项目索引)决定
                            data = sheet.getCellRange(transportReportTemplate.getDataRow() + i, // Row is batch-indexed
                                    transportReportTemplate.getDataColumn() + inspectionDataItemIndex); // Column uses combined item index
                        }
                        String dataString = dataList.get(j).split(":")[1];
                        if (transportReportTemplate.getLanguageType() == 2) {
                            if (i18nMap.containsKey(dataString)) {
                                dataString = i18nMap.get(dataString).get("enUs");
                            }
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            if (i18nMap.containsKey(dataString)) {
                                dataString += "\n" + i18nMap.get(dataString).get("enUs");
                            }
                        }
                        data.setText(dataString);

                        // 检查当前项是否为"到期日"
                        String itemName = dataList.get(j).split(":")[0];
                        if (StrUtil.contains(itemName, "到期日") || StrUtil.contains(itemName, "Expiry")) {
                            // 到期日特殊处理 - 计算实际到期日
                            if (StrUtil.endWith(dataString, "个月") ||
                                    StrUtil.endWithIgnoreCase(dataString, "Months") ||
                                    StrUtil.endWithIgnoreCase(dataString, "Month")) {

                                // 提取月份数字
                                String monthStr = "";
                                if (StrUtil.endWith(dataString, "个月")) {
                                    monthStr = StrUtil.subBefore(dataString, "个月", false);
                                } else if (StrUtil.endWithIgnoreCase(dataString, "Months")) {
                                    monthStr = StrUtil.subBefore(dataString, "Months", true);
                                    monthStr = StrUtil.trim(monthStr);
                                } else if (StrUtil.endWithIgnoreCase(dataString, "Month")) {
                                    monthStr = StrUtil.subBefore(dataString, "Month", true);
                                    monthStr = StrUtil.trim(monthStr);
                                }

                                // 使用正则表达式提取数字
                                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(\\d+)");
                                java.util.regex.Matcher matcher = pattern.matcher(monthStr);
                                if (matcher.find()) {
                                    String monthNumber = matcher.group(1);
                                    int months = Integer.parseInt(monthNumber);
                                    try {
                                        String prodDateStr = transportReportData.getProductionDate();
                                        java.util.Date prodDate = null;
                                        String[] dateFormats = { "yyyy-MM-dd", "yyyy/MM/dd", "yyyy年MM月dd日" };
                                        for (String format : dateFormats) {
                                            try {
                                                java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat(format);
                                                dateFormat.setLenient(false);
                                                prodDate = dateFormat.parse(prodDateStr);
                                                break; 
                                            } catch (Exception e) {/* continue */}
                                        }
                                        if (prodDate != null) {
                                            java.util.Calendar calendar = java.util.Calendar.getInstance();
                                            calendar.setTime(prodDate);
                                            calendar.add(java.util.Calendar.MONTH, months);
                                            calendar.add(java.util.Calendar.DATE, -1);
                                            java.text.SimpleDateFormat outputFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                            String expirationDate = outputFormat.format(calendar.getTime());
                                            dataString = expirationDate; // Update dataString
                                            data.setText(dataString); // Set cell text

                                            CellRange firstCellInRowForDataStyle = sheet.getCellRange(data.getRow(), 1);
                                            data.getCellStyle().setHorizontalAlignment(firstCellInRowForDataStyle.getCellStyle().getHorizontalAlignment());
                                            data.getCellStyle().setVerticalAlignment(firstCellInRowForDataStyle.getCellStyle().getVerticalAlignment());
                                            data.getCellStyle().getExcelFont().setSize(firstCellInRowForDataStyle.getCellStyle().getExcelFont().getSize());
                                        } else { log.warn("到期日处理：无法解析生产日期 {}", prodDateStr); }
                                    } catch (Exception e) { log.error("计算到期日失败", e); }
                                }
                            }

                            // 找到对应的item单元格和requirement单元格 for merging
                            int itemIndexForExpiry = inspectionDataItemIndex; // Current item's index in the data section
                            CellRange itemCellForExpiry, requirementCellForExpiry;

                            if (transportReportTemplate.getItemDirection() != null &&
                                    transportReportTemplate.getItemDirection() == 2) { // Vertical items
                                int baseItemRow = transportReportTemplate.getRequirementRow();
                                int baseItemCol = transportReportTemplate.getRequirementColumn();
                                itemCellForExpiry = sheet.getCellRange(baseItemRow + itemIndexForExpiry, baseItemCol);
                                requirementCellForExpiry = sheet.getCellRange(baseItemRow + itemIndexForExpiry, baseItemCol + 1); // Assuming req is next col
                            } else { // Horizontal items
                                int baseItemRow = transportReportTemplate.getRequirementRow();
                                int baseItemCol = transportReportTemplate.getRequirementColumn();
                                itemCellForExpiry = sheet.getCellRange(baseItemRow, baseItemCol + itemIndexForExpiry);
                                if (transportReportTemplate.getLanguageType() == 3) {
                                    requirementCellForExpiry = sheet.getCellRange(baseItemRow + 2, baseItemCol + itemIndexForExpiry);
                                } else {
                                    requirementCellForExpiry = sheet.getCellRange(baseItemRow + 1, baseItemCol + itemIndexForExpiry);
                                }
                            }
                            CellRange mergeRangeExpiry = sheet.getCellRange(itemCellForExpiry.getRow(), itemCellForExpiry.getColumn(),
                                    requirementCellForExpiry.getRow(), requirementCellForExpiry.getColumn());
                            mergeRangeExpiry.merge();
                            if (transportReportTemplate.getLanguageType() == 3) {
                                mergeRangeExpiry.setText("到期日\n\nExpiry\n\nDate");
                            } else if (transportReportTemplate.getLanguageType() == 2) {
                                // Potentially set English only text if needed, or rely on item.setText for single language
                            }


                            CellRange firstCellInItemRow = sheet.getCellRange(itemCellForExpiry.getRow(), 1);
                            itemCellForExpiry.getCellStyle().setHorizontalAlignment(HorizontalAlignType.Center);
                            itemCellForExpiry.getCellStyle().setVerticalAlignment(VerticalAlignType.Center);
                            itemCellForExpiry.getCellStyle().getExcelFont().setSize(firstCellInItemRow.getCellStyle().getExcelFont().getSize());
                        }
                        inspectionDataItemIndex++;
                    }
                    // 填充数量 (this is per batch, so i is the currentReportDataSet loop index)
                    if (templateFieldSet.contains("批号数量")) {
                        CellRange quantity = sheet.getCellRange(transportReportTemplate.getProductionBatchRow() + i, // i is batch index
                                transportReportTemplate.getPackingSpecificationColumn2());
                        quantity.setText(transportReportData.getNumberOfPackages());
                    }
                }

                // 无检测数据提示 (uses currentReportDataSet.size())
                int i_no_data_prompt_row_start = transportReportTemplate.getProductionBatchRow() + currentReportDataSet.size();
                // 解析合并单元格的起始列位置（用于无检测数据提示和合同号）
                int mergeStartColIndex = parseColumnNameToIndex(transportReportTemplate.getMergeStart());
                
                if (templateFieldSet.contains("无检测数据提示")) {
                    if (transportReportTemplate.getLanguageType() != 2) { // Not for English only
                        
                        sheet.getRange()
                                .get(transportReportTemplate.getMergeStart() + i_no_data_prompt_row_start + ":"
                                        + transportReportTemplate.getMergeEnd()
                                        + i_no_data_prompt_row_start)
                                .merge();
                        // 使用合并单元格的实际起始列位置
                        CellRange merged = sheet.getCellRange(i_no_data_prompt_row_start, mergeStartColIndex);
                        merged.getCellStyle().setHorizontalAlignment(HorizontalAlignType.Center);
                        merged.getCellStyle().setVerticalAlignment(VerticalAlignType.Center);
                        merged.getCellStyle().getExcelFont()
                                .setSize(sheet.getCellRange(transportReportTemplate.getRequirementRow(),
                                        transportReportTemplate.getRequirementColumn()).getCellStyle().getExcelFont()
                                        .getSize());
                        String mergeText = "以下无检验数据";
                        if (transportReportTemplate.getLanguageType() == 3) {
                            mergeText += "\n" + "No Further Inspection Data";
                        }
                        merged.setText(mergeText);
                        if (i_no_data_prompt_row_start > transportReportTemplate.getProductionBatchRow()) { // Avoid error if no batches
                           sheet.setRowHeight(i_no_data_prompt_row_start,
                            sheet.getCellRange(i_no_data_prompt_row_start - 1, 1).getRowHeight());
                        }
                    }
                }
                // 合同号单元格 - 控制单元格创建、合并和样式设置
                CellRange contractNumberCell = null;
                int contractRow = -1;
                if (templateFieldSet.contains("合同号单元格")) {
                    // Position for contract number
                    if (transportReportTemplate.getLanguageType() == 2) {
                        // 英文模板：使用无检测数据提示的位置（因为英文模板没有无检测数据提示）
                        contractRow = i_no_data_prompt_row_start;
                    } else {
                        // 非英文模板：如果有无检测数据提示，合同号显示在下一行
                        contractRow = i_no_data_prompt_row_start + (templateFieldSet.contains("无检测数据提示") ? 1 : 0);
                    }

                    // 合并单元格
                    sheet.getRange()
                            .get(transportReportTemplate.getMergeStart() + contractRow + ":"
                                    + transportReportTemplate.getMergeEnd()
                                    + contractRow)
                            .merge();

                    // 使用合并单元格的实际起始列位置
                    contractNumberCell = sheet.getCellRange(contractRow, mergeStartColIndex);

                    // 设置单元格样式
                    contractNumberCell.getCellStyle().setHorizontalAlignment(HorizontalAlignType.Center);
                    contractNumberCell.getCellStyle().setVerticalAlignment(VerticalAlignType.Center);
                    contractNumberCell.getCellStyle().getExcelFont()
                            .setSize(sheet.getCellRange(transportReportTemplate.getRequirementRow(),
                                    transportReportTemplate.getRequirementColumn()).getCellStyle().getExcelFont()
                                    .getSize());

                    // 设置行高
                    if (contractRow > transportReportTemplate.getProductionBatchRow()) { // Avoid error if no batches
                        sheet.setRowHeight(contractRow, sheet.getCellRange(contractRow - 1, 1).getRowHeight());
                    } else if (currentReportDataSet.isEmpty() && contractRow == transportReportTemplate.getProductionBatchRow()) {
                        // Handle case where there are no batches, set a default height or copy from a known row
                        sheet.setRowHeight(contractRow, 20); // Example default height
                    }
                }

                // 合同号内容 - 控制合同号文本内容的生成和填充
                if (templateFieldSet.contains("合同号内容")) {
                    if (StrUtil.isNotBlank(transferRecord.getOurContractNumber()) ||
                            StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {

                        String contractNumberText = "合同号：";
                        if (StrUtil.isNotBlank(transferRecord.getOurContractNumber())) {
                            contractNumberText += transferRecord.getOurContractNumber();
                            if (StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {
                                contractNumberText += "（" + transferRecord.getTheirContractNumber() + "）";
                            }
                        } else {
                            contractNumberText += transferRecord.getTheirContractNumber();
                        }

                        if (transportReportTemplate.getLanguageType() == 2) {
                            contractNumberText = "Contract No./PO No.：";
                            if (StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {
                                contractNumberText += transferRecord.getTheirContractNumber();
                            }
                        }

                        if (transportReportTemplate.getLanguageType() == 3) {
                            String cnText = "合同号：";
                            if (StrUtil.isNotBlank(transferRecord.getOurContractNumber())) {
                                cnText += transferRecord.getOurContractNumber();
                                if (StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {
                                    cnText += "（" + transferRecord.getTheirContractNumber() + "）";
                                }
                            } else {
                                cnText += transferRecord.getTheirContractNumber();
                            }
                            String enText = "Contract No.："; // PO No. part only if theirContractNumber exists for EN only
                            if (StrUtil.isNotBlank(transferRecord.getOurContractNumber())) { // Assuming OurContract is primary for EN display
                                enText += transferRecord.getOurContractNumber();
                                if (StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {
                                    // Decide if PO should be appended for EN if OurContract exists
                                    // enText += " (PO: " + transferRecord.getTheirContractNumber() + ")";
                                }
                            } else if (StrUtil.isNotBlank(transferRecord.getTheirContractNumber())) {
                                enText += transferRecord.getTheirContractNumber();
                            }
                            contractNumberText = cnText + "\n" + enText;
                        }

                        // 填充文本内容
                        if (!(transportReportTemplate.getLanguageType() == 2
                                && StrUtil.isBlank(transferRecord.getTheirContractNumber()))) {

                            // 如果没有创建合同号单元格，则需要计算位置并获取单元格
                            if (contractNumberCell == null) {
                                if (contractRow == -1) {
                                    // 计算合同号行位置
                                    if (transportReportTemplate.getLanguageType() == 2) {
                                        // 英文模板：使用无检测数据提示的位置（因为英文模板没有无检测数据提示）
                                        contractRow = i_no_data_prompt_row_start;
                                    } else {
                                        // 非英文模板：如果有无检测数据提示，合同号显示在下一行
                                        contractRow = i_no_data_prompt_row_start + (templateFieldSet.contains("无检测数据提示") ? 1 : 0);
                                    }
                                }
                                contractNumberCell = sheet.getCellRange(contractRow, mergeStartColIndex);
                            }

                            contractNumberCell.setText(contractNumberText);
                        }
                    }
                }
                
                // 结论
                if (templateFieldSet.contains("检测结论")) {
                    CellRange result = sheet.getCellRange(transportReportTemplate.getInspectionConclusionRow(),
                            transportReportTemplate.getInspectionConclusionColumn());
                    String inspectionConclusion = transportReport.getInspectionConclusion();
                    if (transportReportTemplate.getLanguageType() == 2) {
                        inspectionConclusion = "Qualified";
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        if (StrUtil.startWith(inspectionConclusion, "符合") && StrUtil.endWith(inspectionConclusion, "标准")) {
                            String englishConclusion = inspectionConclusion.replace("符合", "Conform to ")
                                    .replace("标准", " Standard");
                            inspectionConclusion = inspectionConclusion + "\n" + englishConclusion;
                        } else if (StrUtil.equals(inspectionConclusion, "合格")) {
                            inspectionConclusion = inspectionConclusion + "\nQualified";
                        }
                    }
                    result.setText(inspectionConclusion);
                }
                // 主检
                if (templateFieldSet.contains("主检")) {
                    CellRange inspector = sheet.getCellRange(transportReportTemplate.getInspectorRow(),
                            transportReportTemplate.getInspectorColumn());
                    String inspectorText;
                    if (transportReportTemplate.getItemDirection() != null &&
                            transportReportTemplate.getItemDirection() == 2) {
                        // 竖版排列
                        if (transportReportTemplate.getLanguageType() == 2) {
                            inspectorText = i18nMap.getOrDefault(transportReport.getInspector(), Collections.singletonMap("enUs", transportReport.getInspector())).get("enUs");
                        } else if (transportReportTemplate.getLanguageType() == 3) {
                            inspectorText = transportReport.getInspector() + "\n"
                                    + i18nMap.getOrDefault(transportReport.getInspector(), Collections.singletonMap("enUs", transportReport.getInspector())).get("enUs");
                        } else {
                            inspectorText = transportReport.getInspector();
                        }
                    } else {
                        // 横版排列（原逻辑）
                        inspectorText = "主检：" + transportReport.getInspector();
                        if (transportReportTemplate.getLanguageType() == 2) {
                            inspectorText = "Inspected by：" + i18nMap.getOrDefault(transportReport.getInspector(), Collections.singletonMap("enUs", transportReport.getInspector())).get("enUs");
                        }
                        if (transportReportTemplate.getLanguageType() == 3) {
                            inspectorText += "\nInspected by：" + i18nMap.getOrDefault(transportReport.getInspector(), Collections.singletonMap("enUs", transportReport.getInspector())).get("enUs");
                        }
                    }
                    inspector.setText(inspectorText);
                }
                // 校核
                if (templateFieldSet.contains("校核")) {
                    CellRange auditor1 = sheet.getCellRange(transportReportTemplate.getAuditorRow(),
                            transportReportTemplate.getAuditorColumn());
                    String auditor1Text = "校核：" + transportReport.getAuditor();
                    if (transportReportTemplate.getLanguageType() == 2) {
                        auditor1Text = "Reviewed by：" + i18nMap.getOrDefault(transportReport.getAuditor(), Collections.singletonMap("enUs", transportReport.getAuditor())).get("enUs");
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        auditor1Text += "\nReviewed by：" + i18nMap.getOrDefault(transportReport.getAuditor(), Collections.singletonMap("enUs", transportReport.getAuditor())).get("enUs");
                    }
                    auditor1.setText(auditor1Text);
                }
                // 审核
                if (templateFieldSet.contains("审核")) {
                    CellRange auditor2 = sheet.getCellRange(transportReportTemplate.getVerifierRow(),
                            transportReportTemplate.getVerifierColumn());
                    String auditor2Text = "审核：" + transportReport.getVerifier();
                    if (transportReportTemplate.getLanguageType() == 2) {
                        auditor2Text = "Approved by：" + i18nMap.getOrDefault(transportReport.getVerifier(), Collections.singletonMap("enUs", transportReport.getVerifier())).get("enUs");
                    }
                    if (transportReportTemplate.getLanguageType() == 3) {
                        auditor2Text += "\nApproved by：" + i18nMap.getOrDefault(transportReport.getVerifier(), Collections.singletonMap("enUs", transportReport.getVerifier())).get("enUs");
                    }
                    auditor2.setText(auditor2Text);
                }
                // 日期
                if (templateFieldSet.contains("出单日期")) {
                    CellRange date = sheet.getCellRange(transportReportTemplate.getReportDateRow(),
                            transportReportTemplate.getReportDateColumn());
                    String dateText;
                    if (transportReportTemplate.getItemDirection() != null &&
                            transportReportTemplate.getItemDirection() == 2) {
                        dateText = transportReport.getReportDate();
                    } else {
                        dateText = "出单日期：" + transportReport.getReportDate();
                        if (transportReportTemplate.getLanguageType() == 2) {
                            dateText = "Issue Date：" + transportReport.getReportDate();
                        } else if (transportReportTemplate.getLanguageType() == 3) {
                            dateText += "\nIssue Date：" + transportReport.getReportDate();
                        }
                    }
                    date.setText(dateText);
                }

                // 创建目录对象 (Moved outside inner loop if directory is common)
                File directory = new File(directoryInfo);
                if (!directory.exists()) {
                    if (!directory.mkdirs()) {
                        log.error("文件夹创建失败: {}", directoryInfo);
                        // Potentially skip this report generation or throw
                        continue; 
                    }
                }
                // 文件路径 (currentReportFileBaseName already has batch if needed)
                String pathForCurrentReportFile = directoryInfo + currentReportFileBaseName;

                workbook.getConverterSetting().setSheetFitToPage(true);
                sheet.getPageSetup().setZoom(transportReportTemplate.getZoom());

                String finalFileLangSuffix;
                String excelFileSuffix = "";

                if (transportReportTemplate.getLanguageType() == 2) {
                    finalFileLangSuffix = "_en";
                    excelFileSuffix = "_en_excel.xls"; // Full suffix for Excel
                } else if (transportReportTemplate.getLanguageType() == 3) {
                    finalFileLangSuffix = "_zh_en";
                } else {
                    finalFileLangSuffix = "_zh";
                }
                
                String pdfFilePath = pathForCurrentReportFile + finalFileLangSuffix + ".pdf";
                String svgFilePath = pathForCurrentReportFile + finalFileLangSuffix + ".svg";

                if (transportReportTemplate.getLanguageType() == 2) {
                    // 在生成Excel之前处理权限问题，移除位置靠下的图片
                    // removeBottomImagesForExcel(sheet);
                    
                    long excelStartTime = System.currentTimeMillis();
                    String excelActualPath = pathForCurrentReportFile + excelFileSuffix;
                    log.info("开始生成Excel文件: {}", excelActualPath);
                    workbook.saveToFile(excelActualPath); // Save as .xls for Spire
                    double excelDuration = (System.currentTimeMillis() - excelStartTime) / 1000.0;
                    log.info("Excel文件生成完成，耗时: {}秒", String.format("%.2f", excelDuration));
                }

                long pdfStartTime = System.currentTimeMillis();
                log.info("开始生成PDF文件: {}", pdfFilePath);
                workbook.saveToFile(pdfFilePath, FileFormat.PDF);
                double pdfDuration = (System.currentTimeMillis() - pdfStartTime) / 1000.0;
                log.info("PDF文件生成完成，耗时: {}秒", String.format("%.2f", pdfDuration));

                // 为PDF添加权限保护（防止修改）
                addPdfProtection(pdfFilePath);

                try {
                    long svgStartTime = System.currentTimeMillis();
                    log.info("开始生成SVG文件: {}", svgFilePath);
                    FileOutputStream stream = new FileOutputStream(svgFilePath);
                  
                    sheet.toSVGStream(stream, sheet.getFirstRow(), sheet.getFirstColumn(),
                    transportReportTemplate.getInspectorRow(), // Adjusted to cover more content potentially
                    transportReportTemplate.getQuantityColumn());
                    stream.flush();
                    stream.close();
                    double svgDuration = (System.currentTimeMillis() - svgStartTime) / 1000.0;
                    log.info("SVG文件生成完成，耗时: {}秒", String.format("%.2f", svgDuration));
                } catch (IOException e) {
                    log.error("生成SVG文件失败: " + svgFilePath, e);
                }

                String svgContent = null;
                try {
                    svgContent = new String(Files.readAllBytes(Paths.get(svgFilePath)), StandardCharsets.UTF_8);
                    svgContent = svgContent.replaceAll(
                            "(<text[^>]*?style=\")",
                            "$1font-weight:bold;");
                } catch (IOException e) {
                    log.error("读取SVG文件失败: " + svgFilePath, e);
                }
                if (svgContent != null) {
                    urlList.add(svgContent);
                }
            }
        });
        return urlList;
    }

    @Override
    public List<String> getTransportReportPreview(TransferRecordDTO transferRecordDTO) {
        List<String> urlList = new ArrayList<>();
        // 用于记录每个SVG内容对应的语言类型
        Map<String, String> contentToLanguageMap = new HashMap<>();
        TransportQualityInspectionReport transportReport = transportQualityInspectionReportMapper.selectOne(
                new LambdaQueryWrapper<TransportQualityInspectionReport>()
                        .eq(TransportQualityInspectionReport::getLinkId, transferRecordDTO.getLinkId())
                        .eq(TransportQualityInspectionReport::getOutboundNumber, transferRecordDTO.getOutboundNumber())
                        .eq(TransportQualityInspectionReport::getProductName, transferRecordDTO.getProductName()));
        
        if (transportReport == null) {
            log.warn("未找到用于预览的运输报告: LinkId={}, OutboundNumber={}, ProductName={}", 
                transferRecordDTO.getLinkId(), transferRecordDTO.getOutboundNumber(), transferRecordDTO.getProductName());
            return urlList;
        }

        String baseNamePart = FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + "_"
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + "_"
                + transportReport.getOutboundNumber()
                + "_"
                + transportReport.getId();

        String directoryPath = filePath // 配置文件中的文件路径
                + FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + File.separator 
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + File.separator;

        // 1. 尝试读取旧文件名格式（兼容性）
        String[] langSuffixes = {"_zh.svg", "_zh_en.svg", "_en.svg"};
        for (String suffix : langSuffixes) {
            String oldFilePathString = directoryPath + baseNamePart + suffix;
            try {
                Path oldPath = Paths.get(oldFilePathString);
                if (Files.exists(oldPath)) {
                    String svgContent = new String(Files.readAllBytes(oldPath), StandardCharsets.UTF_8);
                    svgContent = svgContent.replaceAll("(<text[^>]*?style=\")", "$1font-weight:bold;");
                    if (!urlList.contains(svgContent)) {
                        urlList.add(svgContent);
                        // 记录语言类型
                        String langType = "";
                        if (suffix.equals("_zh_en.svg")) {
                            langType = "zhen";
                        } else if (suffix.equals("_zh.svg")) {
                            langType = "zh";
                        } else if (suffix.equals("_en.svg")) {
                            langType = "en";
                        }
                        contentToLanguageMap.put(svgContent, langType);
                    }
                    log.info("成功读取旧格式文件: " + oldFilePathString);
                }
            } catch (IOException e) {
                log.debug("旧格式文件未找到或读取错误: " + oldFilePathString + " (" + e.getMessage() + ")");
            }
        }

        // 2. 扫描目录中新的按批次生成的文件名
        File dir = new File(directoryPath);
        if (dir.exists() && dir.isDirectory()) {
            // 正则表达式匹配: 通用基础名称 + "_" + 批次号 + 语言后缀.svg
            // 示例: Customer_Product_Outbound_ID_Batch123_zh.svg
            String regexPattern = "^" + java.util.regex.Pattern.quote(baseNamePart + "_") + // 通用基础名称_
                                  "[^_]+?" + // 批次号（非贪婪，任何非下划线字符）
                                  "(_zh|_en|_zh_en)\\.svg$"; // 语言后缀.svg
            java.util.regex.Pattern compiledPattern = java.util.regex.Pattern.compile(regexPattern);

            File[] batchFiles = dir.listFiles((d, name) -> compiledPattern.matcher(name).matches());

            if (batchFiles != null) {
                for (File batchFile : batchFiles) {
                    try {
                        String svgContent = new String(Files.readAllBytes(batchFile.toPath()), StandardCharsets.UTF_8);
                        svgContent = svgContent.replaceAll("(<text[^>]*?style=\")", "$1font-weight:bold;");
                        if (!urlList.contains(svgContent)) { // 避免重复
                            urlList.add(svgContent);
                            // 从文件名中提取语言类型
                            String fileName = batchFile.getName();
                            String langType = "";
                            if (fileName.contains("_zh_en.svg")) {
                                langType = "zhen";
                            } else if (fileName.contains("_zh.svg")) {
                                langType = "zh";
                            } else if (fileName.contains("_en.svg")) {
                                langType = "en";
                            }
                            contentToLanguageMap.put(svgContent, langType);
                        }
                        log.info("成功读取新批次格式文件: " + batchFile.getAbsolutePath());
                    } catch (IOException e) {
                        log.error("读取批次特定SVG文件时出错: " + batchFile.getAbsolutePath(), e);
                    }
                }
            }
        }
        // 确保唯一性，如果有重叠则保持顺序，并根据语言类型排序
        if (!urlList.isEmpty()) {
            List<String> uniqueList = new ArrayList<>(new LinkedHashSet<>(urlList));
            // 根据语言类型排序：中文 > 中英文 > 英文
            uniqueList.sort((content1, content2) -> {
                int priority1 = getLanguagePriorityByType(contentToLanguageMap.get(content1));
                int priority2 = getLanguagePriorityByType(contentToLanguageMap.get(content2));
                return Integer.compare(priority1, priority2);
            });
            return uniqueList;
        }
        return urlList;
    }

    /**
     * 根据语言类型获取优先级
     * 中文：1，中英文：2，英文：3
     * 
     * @param langType 语言类型标识
     * @return 优先级数值，数值越小优先级越高
     */
    private int getLanguagePriorityByType(String langType) {
        if (langType == null) {
            return 4; // 未知类型，优先级最低
        }
        switch (langType) {
            case "zh":
                return 1; // 中文优先级最高
            case "zhen":
                return 2; // 中英文优先级中等
            case "en":
                return 3; // 英文优先级最低
            default:
                return 4; // 未知类型，优先级最低
        }
    }

    @Override
    public Boolean resetTransportReport(TransferRecordDTO transferRecordDTO) {
        List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transferRecordDTO.getLinkId())
                        .eq(OutboundInformationTransferRecord::getOutboundNumber,
                                transferRecordDTO.getOutboundNumber())
                        .eq(OutboundInformationTransferRecord::getProductName,
                                transferRecordDTO.getProductName()));
        transferRecordList.forEach(
                transferRecordItem -> {
                    transferRecordItem.setHandleStatus(null);
                    transferRecordItem.setFirstReviewStatus(null);
                    transferRecordItem.setSecondReviewStatus(null);
                    transferRecordMapper.updateById(transferRecordItem);
                });
        transportQualityInspectionReportMapper.delete(
                new LambdaQueryWrapper<TransportQualityInspectionReport>()
                        .eq(TransportQualityInspectionReport::getLinkId, transferRecordDTO.getLinkId())
                        .eq(
                                TransportQualityInspectionReport::getOutboundNumber,
                                transferRecordDTO.getOutboundNumber())
                        .eq(
                                TransportQualityInspectionReport::getProductName,
                                transferRecordDTO.getProductName()));
        return true;
    }

    @Override
    public Boolean reportReview(TransportReportReviewDTO transportReportReviewDTO) {
        TransferRecordDTO transferRecord = transportReportReviewDTO.getTransferRecord();
        List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transferRecord.getLinkId())
                        .eq(
                                OutboundInformationTransferRecord::getOutboundNumber,
                                transferRecord.getOutboundNumber())
                        .eq(
                                OutboundInformationTransferRecord::getProductName,
                                transferRecord.getProductName()));
        transferRecordList.forEach(
                transferRecordItem -> {
                    if (transportReportReviewDTO.getNumber() == 1) {
                        transferRecordItem.setFirstReviewStatus(transportReportReviewDTO.getFlag());
                    }
                    if (transportReportReviewDTO.getNumber() == 2) {
                        transferRecordItem.setSecondReviewStatus(transportReportReviewDTO.getFlag());
                    }
                    transferRecordMapper.updateById(transferRecordItem);
                });
        TransportQualityInspectionReport transportReport = transportQualityInspectionReportMapper.selectOne(
                new LambdaQueryWrapper<TransportQualityInspectionReport>()
                        .eq(TransportQualityInspectionReport::getLinkId, transferRecord.getLinkId())
                        .eq(
                                TransportQualityInspectionReport::getOutboundNumber,
                                transferRecord.getOutboundNumber())
                        .eq(
                                TransportQualityInspectionReport::getProductName,
                                transferRecord.getProductName()));
        if (transportReportReviewDTO.getNumber() == 1) {
            transportReport.setAudit(transportReportReviewDTO.getFlag());
        }
        if (transportReportReviewDTO.getNumber() == 2) {
            transportReport.setVerification(transportReportReviewDTO.getFlag());
        }
        transportQualityInspectionReportMapper.updateById(transportReport);
        return true;
    }

    @Override
    public Map<String, String> downloadTransportReport(
            Integer linkId, String outboundNumber, String productName, String language) {
        // 查询随车质检单
        TransportQualityInspectionReport transportReport = transportQualityInspectionReportMapper.selectOne(
                new LambdaQueryWrapper<TransportQualityInspectionReport>()
                        .eq(TransportQualityInspectionReport::getLinkId, linkId)
                        .eq(TransportQualityInspectionReport::getOutboundNumber, outboundNumber)
                        .eq(TransportQualityInspectionReport::getProductName, productName));
        // 查询出库传递单
        OutboundInformationTransferRecord transferRecord = transferRecordMapper.selectOne(
                new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                        .eq(OutboundInformationTransferRecord::getLinkId, transportReport.getLinkId())
                        .eq(OutboundInformationTransferRecord::getOutboundNumber,
                                transportReport.getOutboundNumber())
                        .eq(OutboundInformationTransferRecord::getProductName,
                                transportReport.getProductName())
                        .groupBy(OutboundInformationTransferRecord::getOutboundNumber));

        if (transportReport == null || transferRecord == null) {
            log.warn("Transport report or transfer record not found for download. LinkId: {}, OutboundNumber: {}, ProductName: {}",
                    linkId, outboundNumber, productName);
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("error", "Report or transfer record not found.");
            return errorMap;
        }

        // 目录信息
        String directoryPath = filePath
                + FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + File.separator
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + File.separator;

        String baseNamePart = FileNameUtil.cleanInvalid(transportReport.getCustomerName())
                + "_"
                + FileNameUtil.cleanInvalid(transportReport.getProductName())
                + "_"
                + transportReport.getOutboundNumber()
                + "_"
                + transportReport.getId();

        String downloadFilePrefix = FileNameUtil.cleanInvalid(transportReport.getProductName())
                + "-"
                + transportReport.getWeightInTons()
                + "吨-"
                + FileNameUtil.cleanInvalid(subPreIgnorePunctuation(transportReport.getCustomerName(), 15))
                + "-"
                + (StrUtil.isNotBlank(transferRecord.getOurContractNumber()) ? transferRecord.getOurContractNumber() : "NoContract") // 添加非空判断
                + "-"
                + transportReport.getId();

        List<File> filesToProcess = new ArrayList<>();
        String targetFileExtension = language.equals("en_excel") ? ".xls" : ".pdf";
        String targetLanguageSuffix = language.equals("en_excel") ? "_en_excel" : "_" + language;
        
        log.info("开始文件查找。请求语言：{}，目标扩展名：{}，目标语言后缀：{}", 
                language, targetFileExtension, targetLanguageSuffix);

        // 1. 查找按批号生成的文件 (使用StrUtil替代正则表达式)
        File dir = new File(directoryPath);
        if (dir.exists() && dir.isDirectory()) {
            File[] batchFiles = dir.listFiles((d, fileName) -> {
                // 使用StrUtil进行字符串匹配，避免复杂的正则表达式
                // 基本格式：baseNamePart_BATCH_NUMBER_LANGUAGE_SUFFIX.EXTENSION
                
                // 1. 检查是否以baseNamePart开头
                if (!StrUtil.startWith(fileName, baseNamePart + "_")) {
                    return false;
                }
                
                // 2. 检查是否以目标后缀结尾
                if (!StrUtil.endWith(fileName, targetLanguageSuffix + targetFileExtension)) {
                    return false;
                }
                
                // 3. 对于英文版请求，精确检查语言后缀（避免合同号、产品名称、批号中的"zh"误判）
                if (StrUtil.equals(language, "en")) {
                    // 从文件名末尾反向检查，精确匹配语言后缀模式
                    String nameWithoutExt = StrUtil.removeSuffix(fileName, targetFileExtension);
                    // 检查是否以中英文混合后缀结尾（_zh_en），如果是则排除
                    if (StrUtil.endWith(nameWithoutExt, "_zh_en")) {
                        return false;
                    }
                    // 确保以纯英文后缀结尾（_en），且不是中英文后缀的一部分
                    if (!StrUtil.endWith(nameWithoutExt, "_en") || StrUtil.endWith(nameWithoutExt, "_zh_en")) {
                        return false;
                    }
                }
                
                // 4. 验证中间是否存在批号部分（确保文件名格式正确）
                String nameWithoutBaseAndSuffix = StrUtil.removePrefix(fileName, baseNamePart + "_");
                nameWithoutBaseAndSuffix = StrUtil.removeSuffix(nameWithoutBaseAndSuffix, targetLanguageSuffix + targetFileExtension);
                
                // 批号部分不能为空，且不应包含路径分隔符
                return StrUtil.isNotBlank(nameWithoutBaseAndSuffix) && 
                       !StrUtil.contains(nameWithoutBaseAndSuffix, File.separator) &&
                       !StrUtil.contains(nameWithoutBaseAndSuffix, "/") &&
                       !StrUtil.contains(nameWithoutBaseAndSuffix, "\\");
            });
            
            if (batchFiles != null && batchFiles.length > 0) {
                filesToProcess.addAll(Arrays.asList(batchFiles));
                log.info("找到批号文件：{}", Arrays.stream(batchFiles).map(File::getName).collect(java.util.stream.Collectors.toList()));
            }
        }

        // 2. 如果没有找到批号文件，查找旧格式单个文件（使用StrUtil进行验证）
        if (filesToProcess.isEmpty()) {
            String singleFileName = baseNamePart + targetLanguageSuffix + targetFileExtension;
            File singleFile = new File(directoryPath + singleFileName);
            if (singleFile.exists()) {
                // 使用StrUtil验证文件名是否精确匹配
                String fileName = singleFile.getName();
                boolean isValidFile = true;
                
                // 对于英文版请求，精确检查语言后缀（避免合同号、产品名称、批号中的"zh"误判）
                if (StrUtil.equals(language, "en")) {
                    // 从文件名末尾反向检查，精确匹配语言后缀模式
                    String nameWithoutExt = StrUtil.removeSuffix(fileName, targetFileExtension);
                    // 检查是否以中英文混合后缀结尾（_zh_en），如果是则排除
                    if (StrUtil.endWith(nameWithoutExt, "_zh_en")) {
                        log.warn("发现了中英文版本文件，但用户请求的是纯英文版本：{}", fileName);
                        isValidFile = false;
                    }
                    // 确保以纯英文后缀结尾（_en），且不是中英文后缀的一部分
                    else if (!StrUtil.endWith(nameWithoutExt, "_en")) {
                        log.warn("文件名不是以纯英文后缀结尾：{}", fileName);
                        isValidFile = false;
                    }
                }
                
                if (isValidFile) {
                    filesToProcess.add(singleFile);
                    log.info("找到旧格式单个文件：{}", fileName);
                }
            }
        }

        // 3. 如果仍然没有找到文件，记录详细的调试信息
        if (filesToProcess.isEmpty()) {
            log.warn("未找到匹配的文件。参数信息：language={}, targetLanguageSuffix={}, targetFileExtension={}", 
                    language, targetLanguageSuffix, targetFileExtension);
            log.warn("查找的目录：{}", directoryPath);
            log.warn("查找的单个文件名：{}", baseNamePart + targetLanguageSuffix + targetFileExtension);
            
            // 列出目录中实际存在的文件用于调试
            File[] allFiles = dir.listFiles();
            if (allFiles != null) {
                log.warn("目录中实际存在的文件：");
                for (File file : allFiles) {
                    log.warn("  - {}", file.getName());
                }
            }
        }

        Map<String, String> fileMap = new HashMap<>();

        if (filesToProcess.isEmpty()) {
            log.warn("未找到指定语言版本的文件。参数信息：LinkId={}, OutboundNumber={}, ProductName={}, Language={}",
                    linkId, outboundNumber, productName, language);
            fileMap.put("error", "未找到指定语言版本的文件，请确认文件是否已生成。");
            return fileMap;
        }

        // 使用StrUtil验证找到的文件是否确实匹配请求的语言类型
        boolean hasMatchingLanguageFile = filesToProcess.stream()
                .anyMatch(file -> {
                    String fileName = file.getName();
                    boolean containsTargetSuffix = StrUtil.contains(fileName, targetLanguageSuffix);
                    // 对于英文版请求，精确检查语言后缀（避免合同号、产品名称、批号中的"zh"误判）
                    if (StrUtil.equals(language, "en")) {
                        // 从文件名末尾反向检查，精确匹配语言后缀模式
                        String nameWithoutExt = StrUtil.removeSuffix(fileName, targetFileExtension);
                        // 必须以纯英文后缀结尾（_en），且不能是中英文后缀（_zh_en）
                        boolean isEnOnly = StrUtil.endWith(nameWithoutExt, "_en") && !StrUtil.endWith(nameWithoutExt, "_zh_en");
                        return containsTargetSuffix && isEnOnly;
                    }
                    return containsTargetSuffix;
                });
        
        if (!hasMatchingLanguageFile) {
            log.warn("找到的文件与请求的语言类型不匹配。请求语言：{}，目标后缀：{}，找到的文件：{}", 
                    language, targetLanguageSuffix, 
                    filesToProcess.stream().map(File::getName).collect(java.util.stream.Collectors.toList()));
            fileMap.put("error", "找到的文件与请求的语言类型不匹配。");
            return fileMap;
        }

        if (filesToProcess.size() == 1) {
            File fileToDownload = filesToProcess.get(0);
            fileMap.put("directoryInfo", fileToDownload.getParent() + File.separator);
            fileMap.put("fileName", fileToDownload.getName());
            // 确保downloadFileName与原逻辑一致，加上正确的后缀
            fileMap.put("downloadFileName", downloadFilePrefix + targetLanguageSuffix.replace("_en_excel", "_en") + targetFileExtension);
            fileMap.put("isZip", "false");
        } else {
            // 多个文件，打包成ZIP
            String zipFileName = baseNamePart + targetLanguageSuffix + "_reports.zip";
            String zipFilePath = directoryPath + zipFileName;
            // 确保downloadFileName对于ZIP包也有意义
            String downloadZipName = downloadFilePrefix + targetLanguageSuffix.replace("_en_excel", "_en") + "_reports.zip";

            try (FileOutputStream fos = new FileOutputStream(zipFilePath);
                 java.util.zip.ZipOutputStream zos = new java.util.zip.ZipOutputStream(fos)) {
                for (File file : filesToProcess) {
                    if (file.exists()) { // 确保文件存在才添加
                        java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(file.getName());
                        zos.putNextEntry(zipEntry);
                        Files.copy(file.toPath(), zos);
                        zos.closeEntry();
                    } else {
                        log.warn("File not found during ZIP creation, skipping: {}", file.getAbsolutePath());
                    }
                }
            } catch (IOException e) {
                log.error("Error creating ZIP file for download: {}", zipFilePath, e);
                fileMap.put("error", "Error creating ZIP file.");
                return fileMap;
            }
            fileMap.put("directoryInfo", directoryPath);
            fileMap.put("fileName", zipFileName); // ZIP文件的名称
            fileMap.put("downloadFileName", downloadZipName);
            fileMap.put("isZip", "true");
        }
        // 记录下载调用移到Controller层，因为Service层不应该直接依赖User对象或进行特定用户操作的记录，除非User ID被传入
        //  saveTransportReportDownloadRecord(linkId, outboundNumber, productName, language, /* 需要 userId */);
        return fileMap;
    }

    @Override
    public Boolean saveTransportReportDownloadRecord(Integer linkId, String outboundNumber, String productName,
            String language, Integer userId) {
        TransportReportDownloadRecord transportReportDownloadRecord = new TransportReportDownloadRecord();
        transportReportDownloadRecord.setLinkId(linkId);
        transportReportDownloadRecord.setOutboundNumber(outboundNumber);
        transportReportDownloadRecord.setProductName(productName);
        transportReportDownloadRecord.setLanguage(language);
        transportReportDownloadRecord.setUserId(userId);
        User user = userMapper.selectById(userId);
        transportReportDownloadRecord.setUserName(user.getUsername());
        return transportReportDownloadRecordMapper.insert(transportReportDownloadRecord) > 0;
    }

    @Override
    public Staff getStaff(User user) {
        Staff staff = staffMapper.selectOne(new LambdaQueryWrapper<Staff>().eq(Staff::getUserId, user.getId()));
        if (ObjUtil.isNull(staff)) {
            return new Staff();
        }
        return staff;
    }

    @Override
    public Boolean setStaff(Staff staff) {
        User user = userMapper.selectById(staff.getUserId());
        if (ObjUtil.isNull(staff.getId())) {
            staff.setLinkId(user.getLinkId());
            staffMapper.insert(staff);
        } else {
            staffMapper.updateById(staff);
        }
        return true;
    }

    @Override
    public Boolean resultJudgment(ResultJudgmentDTO resultJudgmentDTO) {
        String item = resultJudgmentDTO.getItem();
        String requirement = resultJudgmentDTO.getRequirement().get(resultJudgmentDTO.getItem());
        String data = resultJudgmentDTO.getData();
        Integer qualified = 1;
        if (NumberUtil.isNumber(data)) {
            qualified = ItemCalculator.isQualified(
                    resultJudgmentDTO.getRequirement().get(resultJudgmentDTO.getItem()),
                    NumberUtil.parseFloat(data));
        }

        // 外观特殊处理
        if (item.contains("外观")) {
            String[] keywords = { "粉", "粒", "片", "板", "固体", "液体" };

            // 获取指标中包含的所有关键词
            Set<String> requirementKeywords = Arrays.stream(keywords)
                    .filter(requirement::contains)
                    .collect(Collectors.toSet());

            // 只有当指标中包含关键词时才进行检查
            if (!requirementKeywords.isEmpty()) {
                String resultStr = String.valueOf(data);

                // 检查结果是否包含指标中的任意关键词
                boolean resultHasMatchingKeyword = requirementKeywords.stream()
                        .anyMatch(resultStr::contains);

                if (!resultHasMatchingKeyword) {
                    qualified = 0;
                }
            }
        }

        return qualified != null && qualified > 0;
    }

    @Override
    public Map<String, Object> getProductionBatchOutboundRecord(
            TransportReportCreatDTO transportReportCreatDTO) {
        boolean outbound = false;
        // 出库传递单DTO
        TransferRecordDTO transferRecord = transportReportCreatDTO.getTransferRecord();
        // 查询出库传递单客户信息
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, transferRecord.getCustomerNumber()));
        TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                new LambdaQueryWrapper<TransportReportCompany>()
                        .eq(TransportReportCompany::getCompanyNumber, transferRecord.getCustomerNumber()));

        // 客户编号集合
        List<String> customerNumberList = new ArrayList<>();

        // 处理客户所属集团情况
        if (ObjUtil.isNotNull(company)) {
            // 处理普通集团情况
            handleNormalGroup(company, customerNumberList);
        } else if (ObjUtil.isNotNull(transportReportCompany)) {
            // 处理运输报告集团情况
            handleTransportReportGroup(transportReportCompany, customerNumberList);
        } else {
            // 客户不存在所属集团
            customerNumberList.add(transferRecord.getCustomerNumber());
        }

        // 遍历随车质检单检测数据
        for (TransportReportCreatDataDTO transportReportCreatData : transportReportCreatDTO
                .getTransportReportCreatDataList()) {
            List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                    new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                            // 排除当前出库传递单
                            .ne(OutboundInformationTransferRecord::getOutboundNumber,
                                    transferRecord.getOutboundNumber())
                            // 客户编号
                            .in(OutboundInformationTransferRecord::getCustomerNumber, customerNumberList)
                            // 产品名称
                            .eq(OutboundInformationTransferRecord::getProductName,
                                    transferRecord.getProductName())
                            // 生产批号
                            .eq(OutboundInformationTransferRecord::getOutboundProductionBatch,
                                    transportReportCreatData.getOutboundProductionBatch())
                            .eq(OutboundInformationTransferRecord::getHandleStatus, true)
                            .groupBy(OutboundInformationTransferRecord::getOutboundNumber)
                            .orderByDesc(OutboundInformationTransferRecord::getId));
            // 当前生产批号是否存在历史发货记录
            if (CollUtil.isNotEmpty(transferRecordList)) {
                outbound = true;
                OutboundInformationTransferRecord selectTransferRecord = transferRecordList.get(0);
                // 查询历史发货随车质检单
                TransportQualityInspectionReport historicalTransportReport = transportQualityInspectionReportMapper
                        .selectOne(new LambdaQueryWrapper<TransportQualityInspectionReport>()
                                .eq(TransportQualityInspectionReport::getLinkId,
                                        selectTransferRecord.getLinkId())
                                .eq(TransportQualityInspectionReport::getOutboundNumber,
                                        selectTransferRecord.getOutboundNumber())
                                .eq(TransportQualityInspectionReport::getProductName,
                                        selectTransferRecord.getProductName()));
                // 查询历史发货随车质检单质检数据
                TransportQualityInspectionData historicalTransportData = transportQualityInspectionDataMapper.selectOne(
                        new LambdaQueryWrapper<TransportQualityInspectionData>()
                                .eq(TransportQualityInspectionData::getReportId,
                                        historicalTransportReport.getId())
                                .eq(TransportQualityInspectionData::getProductionBatch,
                                        transportReportCreatData.getOutboundProductionBatch()));
                // 设置历史质检数据
                transportReportCreatData.setQualityData(historicalTransportData.getRawInspectionData());
                // 设置是否存在历史发货记录
                transportReportCreatData.setIsHistory(true);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("outbound", outbound);
        resultMap.put("transportReportCreatDTO", transportReportCreatDTO);
        return resultMap;
    }

    @Override
    public Boolean getProductionBatchInspectionData(TransportReportDTO transportReportDTO) {
        boolean differentData = true;
        // 出库传递单DTO
        TransferRecordDTO transferRecord = transportReportDTO.getTransferRecord();
        // 随车质检单
        TransportQualityInspectionReport transportReport = transportReportDTO.getTransportQualityInspectionReport();
        List<Map<String, Object>> requirementColumns = transportReportDTO.getRequirementColumns();

        // 查询出库传递单客户信息
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, transferRecord.getCustomerNumber()));
        TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                new LambdaQueryWrapper<TransportReportCompany>()
                        .eq(TransportReportCompany::getCompanyNumber, transferRecord.getCustomerNumber()));

        // 客户编号集合
        List<String> customerNumberList = new ArrayList<>();

        // 处理客户所属集团情况
        if (ObjUtil.isNotNull(company)) {
            // 处理普通集团情况
            handleNormalGroup(company, customerNumberList);
        } else if (ObjUtil.isNotNull(transportReportCompany)) {
            // 处理运输报告集团情况
            handleTransportReportGroup(transportReportCompany, customerNumberList);
        } else {
            // 客户不存在所属集团
            customerNumberList.add(transferRecord.getCustomerNumber());
        }

        // 遍历随车质检数据
        for (Map<String, Object> qualityInspectionData : transportReportDTO.getQualityInspectionData()) {
            List<OutboundInformationTransferRecord> transferRecordList = transferRecordMapper.selectList(
                    new LambdaQueryWrapper<OutboundInformationTransferRecord>()
                            // 排除当前出库传递单
                            .ne(OutboundInformationTransferRecord::getOutboundNumber,
                                    transferRecord.getOutboundNumber())
                            // 客户编号
                            .in(OutboundInformationTransferRecord::getCustomerNumber, customerNumberList)
                            // 产品名称
                            .eq(OutboundInformationTransferRecord::getProductName,
                                    transferRecord.getProductName())
                            // 生产批号不同
                            .ne(OutboundInformationTransferRecord::getOutboundProductionBatch,
                                    qualityInspectionData.get("productionBatch"))
                            // 质检批号相同
                            .eq(OutboundInformationTransferRecord::getQualityInspectionBatch,
                                    qualityInspectionData.get("qualityInspectionBatch"))
                            // 处理状态
                            .eq(OutboundInformationTransferRecord::getHandleStatus, true)
                            .groupBy(OutboundInformationTransferRecord::getOutboundNumber));
            // 当前质检批号是否存在历史发货记录
            if (CollUtil.isNotEmpty(transferRecordList)) {
                // 对应历史发货出库单编号集合
                List<String> outboundNumberList = transferRecordList.stream()
                        .map(OutboundInformationTransferRecord::getOutboundNumber)
                        .collect(Collectors.toList());
                // 查询对应历史发货随车质检单
                List<TransportQualityInspectionReport> transportReportList = transportQualityInspectionReportMapper
                        .selectList(new LambdaQueryWrapper<TransportQualityInspectionReport>()
                                .eq(TransportQualityInspectionReport::getLinkId, transferRecord.getLinkId())
                                .in(TransportQualityInspectionReport::getOutboundNumber, outboundNumberList)
                                .eq(TransportQualityInspectionReport::getProductName,
                                        transportReport.getProductName()));
                // 历史发货随车质检单ID集合
                List<Integer> transportReportIdList = transportReportList.stream()
                        .map(TransportQualityInspectionReport::getId)
                        .collect(Collectors.toList());
                // 历史发货随车质检单质检数据集合
                List<TransportQualityInspectionData> inspectionDataList = transportQualityInspectionDataMapper
                        .selectList(new LambdaQueryWrapper<TransportQualityInspectionData>()
                                .in(TransportQualityInspectionData::getReportId, transportReportIdList)
                                // 排除当前生产批号
                                .ne(TransportQualityInspectionData::getProductionBatch,
                                        qualityInspectionData.get("productionBatch"))
                                // 查询当前质检批号
                                .apply("JSON_UNQUOTE(JSON_EXTRACT(raw_inspection_data, '$.qualityInspectionBatch')) = {0}",
                                        qualityInspectionData.get("qualityInspectionBatch")));
                // 遍历历史质检数据集合
                for (TransportQualityInspectionData data : inspectionDataList) {
                    // 记录相同数据检测项目的个数
                    int sameDataCount = 0;
                    // 历史质检数据原始记录
                    Map<String, Object> rawInspectionData = data.getRawInspectionData();
                    // 遍历检测项目
                    for (Map<String, Object> itemMap : requirementColumns) {
                        // 检测项目
                        String item = itemMap.get("label").toString();
                        String itemData = Optional.ofNullable(qualityInspectionData.get(item))
                                .map(Object::toString)
                                .orElse("");
                        String rawItemData = Optional.ofNullable(rawInspectionData.get(item)).map(Object::toString)
                                .orElse("");
                        if (StrUtil.isNotBlank(rawItemData)) {
                            // 历史记录检测项目数据与当前记录相同
                            if (StrUtil.equals(itemData, rawItemData)) {
                                sameDataCount++;
                            }
                        } else {
                            sameDataCount++;
                        }
                    }
                    // 全部检测项目数据均相同
                    if (sameDataCount == requirementColumns.size()) {
                        differentData = false;
                        break;
                    }
                }
                // 如果 differentData 为 false，停止外部循环
                if (!differentData) {
                    break; // 退出外部循环
                }
            }
        }
        return differentData;
    }

    @Override
    public TransportReportOnlineAvailability changeOnlineAvailable(TransferRecordDTO transferRecordDTO) {
        // 先查询是否存在记录
        TransportReportOnlineAvailability existingRecord = transportReportOnlineAvailabilityMapper.selectOne(
                new LambdaQueryWrapper<TransportReportOnlineAvailability>()
                        .eq(TransportReportOnlineAvailability::getCustomerNumber, transferRecordDTO.getCustomerNumber())
                        .eq(TransportReportOnlineAvailability::getProductCategoryNumber,
                                transferRecordDTO.getProductCategoryNumber()));

        // 如果记录不存在,创建新记录
        if (ObjUtil.isNull(existingRecord)) {
            existingRecord = new TransportReportOnlineAvailability();
            existingRecord.setCustomerNumber(transferRecordDTO.getCustomerNumber());
            existingRecord.setCustomerName(transferRecordDTO.getCustomerName());
            existingRecord.setProductCategoryNumber(transferRecordDTO.getProductCategoryNumber());
            existingRecord.setProductNumber(transferRecordDTO.getProductNumber());
            existingRecord.setProductName(transferRecordDTO.getProductName());
            existingRecord.setOperatorId(transferRecordDTO.getUser().getId());
            existingRecord.setOperatorName(transferRecordDTO.getUser().getUsername());
            if (ObjUtil.isNull(transferRecordDTO.getOnlineAvailable())) {
                existingRecord.setOnlineAvailable(1);
            } else {
                if (transferRecordDTO.getOnlineAvailable() == 1) {
                    existingRecord.setOnlineAvailable(2);
                } else if (transferRecordDTO.getOnlineAvailable() == 2) {
                    existingRecord.setOnlineAvailable(3);
                } else if (transferRecordDTO.getOnlineAvailable() == 0) {
                    existingRecord.setOnlineAvailable(1);
                } else if (transferRecordDTO.getOnlineAvailable() == 3) {
                    existingRecord.setOnlineAvailable(0);
                }
            }
            transportReportOnlineAvailabilityMapper.insert(existingRecord);
        } else {
            // 记录存在,更新状态
            existingRecord.setOperateTime(null);
            existingRecord.setOperatorId(transferRecordDTO.getUser().getId());
            existingRecord.setOperatorName(transferRecordDTO.getUser().getUsername());
            if (ObjUtil.isNull(transferRecordDTO.getOnlineAvailable())) {
                existingRecord.setOnlineAvailable(1);
            } else {
                if (transferRecordDTO.getOnlineAvailable() == 1) {
                    existingRecord.setOnlineAvailable(2);
                } else if (transferRecordDTO.getOnlineAvailable() == 2) {
                    existingRecord.setOnlineAvailable(3);
                } else if (transferRecordDTO.getOnlineAvailable() == 0) {
                    existingRecord.setOnlineAvailable(1);
                } else if (transferRecordDTO.getOnlineAvailable() == 3) {
                    existingRecord.setOnlineAvailable(0);
                }
            }
            transportReportOnlineAvailabilityMapper.updateById(existingRecord);
        }

        return existingRecord;
    }

    /**
     * 处理附加要求信息
     * <p>
     * 该方法根据客户编号、产品编号和产品类别编号查找相关的附加要求模板，
     * 并将这些额外要求应用到记录要求映射中。方法会查询与客户关联的模板，
     * 然后根据产品类别和产品编号筛选适用的要求，最后更新记录要求映射。
     * </p>
     *
     * @param recordRequirementMap  记录要求映射，存储项目要求的键值对集合
     * @param customerNumber        客户编号，用于查询关联的附加要求模板
     * @param productNumber         产品编号，用于筛选适用的附加要求
     * @param productCategoryNumber 产品类别编号，用于筛选适用的附加要求
     */
    private void handleAdditionalRequirement(
            Map<String, Map<String, String>> recordRequirementMap,
            String customerNumber,
            String productNumber,
            String productCategoryNumber) {

        // 1. 查找客户关联的模板
        List<AdditionalRequirementCustomer> customers = additionalRequirementCustomerMapper.selectList(
                new LambdaQueryWrapper<AdditionalRequirementCustomer>()
                        .eq(AdditionalRequirementCustomer::getCustomerNumber, customerNumber)
                        .ne(AdditionalRequirementCustomer::getDeleteStatus, true));

        if (CollUtil.isEmpty(customers)) {
            return;
        }

        // 2. 获取所有相关模板ID
        List<Integer> templateIds = customers.stream()
                .map(AdditionalRequirementCustomer::getTemplateId)
                .collect(Collectors.toList());
        // 过滤掉已删除的模板ID：查询AdditionalRequirementTemplate中未被删除的记录，并更新templateIds
        List<Integer> validTemplateIds = additionalRequirementTemplateMapper.selectList(
                new LambdaQueryWrapper<AdditionalRequirementTemplate>()
                        .in(AdditionalRequirementTemplate::getId, templateIds)
                        .ne(AdditionalRequirementTemplate::getDeleteStatus, true))
                .stream()
                .map(AdditionalRequirementTemplate::getId)
                .collect(Collectors.toList());
        templateIds = validTemplateIds;

        if (CollUtil.isEmpty(templateIds)) {
            return;
        }

        // 3. 验证产品是否在这些模板中
        List<AdditionalRequirementProductCategory> products = additionalRequirementProductCategoryMapper.selectList(
                new LambdaQueryWrapper<AdditionalRequirementProductCategory>()
                        .in(AdditionalRequirementProductCategory::getTemplateId, templateIds)
                        .eq(AdditionalRequirementProductCategory::getProductCategoryNumber, productCategoryNumber)
                        .eq(AdditionalRequirementProductCategory::getProductNumber, productNumber)
                        .ne(AdditionalRequirementProductCategory::getDeleteStatus, true));

        // 4. 如果没有精确匹配，查找类别匹配的记录
        if (CollUtil.isEmpty(products)) {
            products = additionalRequirementProductCategoryMapper.selectList(
                    new LambdaQueryWrapper<AdditionalRequirementProductCategory>()
                            .in(AdditionalRequirementProductCategory::getTemplateId, templateIds)
                            .eq(AdditionalRequirementProductCategory::getProductCategoryNumber, productCategoryNumber)
                            .and(wrapper -> wrapper
                                    .isNull(AdditionalRequirementProductCategory::getProductNumber)
                                    .or()
                                    .eq(AdditionalRequirementProductCategory::getProductNumber, ""))
                            .ne(AdditionalRequirementProductCategory::getDeleteStatus, true));
        }

        if (CollUtil.isEmpty(products)) {
            return;
        }

        // 5. 获取所有产品ID
        List<Integer> productIds = products.stream()
                .map(AdditionalRequirementProductCategory::getId)
                .collect(Collectors.toList());

        // 获取products包含的全部模板id
        List<Integer> templateIdList = products.stream()
                .map(AdditionalRequirementProductCategory::getTemplateId)
                .distinct()
                .collect(Collectors.toList());

        if (templateIdList.size() == 1) {
            List<TransportReportAdditionalRequirement> requirementList = additionalRequirementMapper.selectList(
                    new LambdaQueryWrapper<TransportReportAdditionalRequirement>()
                            .in(TransportReportAdditionalRequirement::getTemplateId, templateIdList)
                            .ne(TransportReportAdditionalRequirement::getDeleteStatus, true));

            // 6. 获取产品的检测项目详情
            List<AdditionalRequirementDetails> details = additionalRequirementDetailsMapper.selectList(
                    new LambdaQueryWrapper<AdditionalRequirementDetails>()
                            .in(AdditionalRequirementDetails::getProductId, productIds)
                            .eq(AdditionalRequirementDetails::getShowStatus, true)
                            .ne(AdditionalRequirementDetails::getDeleteStatus, true));
            // 将details转为map,key为itemName
            Map<String, AdditionalRequirementDetails> detailsMap = details.stream()
                    .collect(Collectors.toMap(AdditionalRequirementDetails::getItemName, detail -> detail));

            // 7. 处理每个检测项目
            requirementList.forEach(requirement -> {
                if (requirement.getShowStatus()) {
                    AdditionalRequirementDetails detail = detailsMap.get(requirement.getItemName());
                    if (ObjUtil.isNotNull(detail)) {
                        Map<String, String> itemMap = new HashMap<>();
                        itemMap.put("matchingName", detail.getItemName());
                        itemMap.put("itemName", detail.getCustomerItemName());
                        itemMap.put("requirement", detail.getRequirement());
                        // 更新或添加检测项目
                        recordRequirementMap.put(requirement.getItemName(), itemMap);
                    }
                } else {
                    recordRequirementMap.remove(requirement.getItemName());
                }
            });
        }
    }

    /**
     * 去除隐藏的检测项目(数据库)
     *
     * @param recordRequirementMap  指标map
     * @param customerNumber        客户编号
     * @param productNumber         产品编号
     * @param productCategoryNumber 产品类别编号
     */
    private void removeHiddenItems(
            Map<String, Map<String, String>> recordRequirementMap,
            String requirementCategory,
            String customerNumber,
            String productNumber,
            String productCategoryNumber) {
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
        if (ObjUtil.isNotNull(company)) {
            removeGroupHiddenItems(recordRequirementMap, requirementCategory, company, productNumber,
                    productCategoryNumber);
        } else {
            removeGeneralHiddenItems(recordRequirementMap, productNumber, productCategoryNumber);
        }
    }

    /**
     * 去除隐藏的通用检测项目(数据库)
     *
     * @param recordRequirementMap  指标map
     * @param productNumber         产品编号
     * @param productCategoryNumber 产品类别编号
     */
    private void removeGeneralHiddenItems(
            Map<String, Map<String, String>> recordRequirementMap,
            String productNumber,
            String productCategoryNumber) {
        List<GeneralQualityStandard> generalQualityStandardList = generalQualityStandardMapper.selectList(
                new LambdaQueryWrapper<GeneralQualityStandard>()
                        .eq(GeneralQualityStandard::getProductNumber, productNumber));
        if (CollUtil.isEmpty(generalQualityStandardList)) {
            generalQualityStandardList = generalQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GeneralQualityStandard>()
                            .isNull(GeneralQualityStandard::getProductNumber)
                            .eq(GeneralQualityStandard::getProductCategoryNumber, productCategoryNumber));
            if (CollUtil.isNotEmpty(generalQualityStandardList)) {
                generalQualityStandardList.forEach(
                        generalQualityStandard -> {
                            if (!generalQualityStandard.getShowStatus()) {
                                recordRequirementMap.remove(generalQualityStandard.getQualityInspectionItem());
                            }
                        });
            }
        }
    }

    /**
     * 去除隐藏的客户检测项目(数据库)
     *
     * @param recordRequirementMap  指标map
     * @param company               客户信息
     * @param productNumber         产品编号
     * @param productCategoryNumber 产品类别编号
     */
    private void removeGroupHiddenItems(
            Map<String, Map<String, String>> recordRequirementMap,
            String requirementCategory,
            Company company,
            String productNumber,
            String productCategoryNumber) {
        List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectList(
                new LambdaQueryWrapper<GroupQualityStandard>()
                        .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                        .eq(GroupQualityStandard::getProductNumber, productNumber)
                        .eq(StrUtil.isNotBlank(requirementCategory), GroupQualityStandard::getRequirementCategory,
                                requirementCategory));
        if (CollUtil.isEmpty(groupQualityStandardList)) {
            groupQualityStandardList = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                            .isNull(GroupQualityStandard::getProductNumber)
                            .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryNumber)
                            .eq(StrUtil.isNotBlank(requirementCategory), GroupQualityStandard::getRequirementCategory,
                                    requirementCategory));
            if (CollUtil.isNotEmpty(groupQualityStandardList)) {
                groupQualityStandardList.forEach(
                        groupQualityStandard -> {
                            if (!groupQualityStandard.getShowStatus()) {
                                recordRequirementMap.remove(groupQualityStandard.getQualityInspectionItem());
                            }
                        });
            } else {
                removeGeneralHiddenItems(recordRequirementMap, productNumber, productCategoryNumber);
            }
        }
    }

    /**
     * 获取执行标准（数据库）
     *
     * @param customerNumber        客户编号
     * @param productNumber         产品名称
     * @param productCategoryNumber 产品类别
     * @return 执行标准
     */
    private String getExecutionStandards(
            String customerNumber, String productNumber, String productCategoryNumber) {
        String executionStandards = "";
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
        if (ObjUtil.isNotNull(company)) {
            List<GroupQualityStandard> groupQualityStandardList = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                            .eq(GroupQualityStandard::getProductNumber, productNumber)
                            .eq(GroupQualityStandard::getQualityInspectionItem, "规格依据"));
            GroupQualityStandard groupQualityStandard = CollUtil.isNotEmpty(groupQualityStandardList)
                    ? groupQualityStandardList.get(0)
                    : null;
            if (ObjUtil.isNull(groupQualityStandard)) {
                List<GroupQualityStandard> categoryStandardList = groupQualityStandardMapper.selectList(
                        new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                .isNull(GroupQualityStandard::getProductNumber)
                                .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryNumber)
                                .eq(GroupQualityStandard::getQualityInspectionItem, "规格依据"));
                groupQualityStandard = CollUtil.isNotEmpty(categoryStandardList) ? categoryStandardList.get(0) : null;
            }
            if (ObjUtil.isNotNull(groupQualityStandard)) {
                executionStandards = groupQualityStandard.getQualityRequirement();
            }
        }
        if (StrUtil.isBlank(executionStandards)) {
            List<GeneralQualityStandard> generalQualityStandardList = generalQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GeneralQualityStandard>()
                            .eq(GeneralQualityStandard::getProductNumber, productNumber)
                            .eq(GeneralQualityStandard::getQualityInspectionItem, "规格依据"));
            GeneralQualityStandard generalQualityStandard = CollUtil.isNotEmpty(generalQualityStandardList)
                    ? generalQualityStandardList.get(0)
                    : null;
            if (ObjUtil.isNull(generalQualityStandard)) {
                List<GeneralQualityStandard> categoryStandardList = generalQualityStandardMapper.selectList(
                        new LambdaQueryWrapper<GeneralQualityStandard>()
                                .isNull(GeneralQualityStandard::getProductNumber)
                                .eq(GeneralQualityStandard::getProductCategoryNumber, productCategoryNumber)
                                .eq(GeneralQualityStandard::getQualityInspectionItem, "规格依据"));
                generalQualityStandard = CollUtil.isNotEmpty(categoryStandardList) ? categoryStandardList.get(0) : null;
            }
            if (ObjUtil.isNotNull(generalQualityStandard)) {
                executionStandards = generalQualityStandard.getQualityRequirement();
            }
        }
        return executionStandards;
    }

    /**
     * 获取指标map
     *
     * @param requirement 指标字符串
     * @return 指标map
     */
    private static Map<String, Map<String, String>> getRequirementMap(String requirement) {
        // 创建指标map
        Map<String, Map<String, String>> requirementMap = new HashMap<>();
        // 无对应指标
        if (requirement == null) {
            return requirementMap;
        }
        // 处理指标字符串
        // 分割检测项目
        String[] pairs = requirement.split(";");
        // 遍历检测项目
        for (String pair : pairs) {
            // 分割检测项目与指标
            String[] keyValue = pair.split(":");
            // 校验指标字符串格式
            if (keyValue.length == 2) {
                // 检测项目
                String item = keyValue[0];
                // 检测指标
                String itemRequirement = keyValue[1];
                // 指标map
                Map<String, String> itemMap = new HashMap<>();
                // 判断指标字符串格式
                if (item.contains("|")) {
                    // 新版指标格式
                    // 分割项目信息
                    String[] split = item.split("\\|");
                    // 指标显示状态
                    String itemDisplayStatus = split[2];
                    if (StrUtil.equals(itemDisplayStatus, "1")) {
                        itemMap.put("matchingName", split[0]);
                        itemMap.put("itemName", split[1]);
                        itemMap.put("requirement", itemRequirement);
                        if (split.length > 2) {
                            itemMap.put("showStatus", split[2]);
                        }
                        requirementMap.put(split[0], itemMap);
                    }
                } else {
                    // 旧版指标格式
                    itemMap.put("matchingName", item);
                    itemMap.put("itemName", item);
                    itemMap.put("requirement", itemRequirement);
                    itemMap.put("showStatus", "1");
                    requirementMap.put(item, itemMap);
                }
            }
        }
        return requirementMap;
    }

    /**
     * 获取检测项目优先级
     *
     * @param commonItemPriorityList 通用检测项目优先级集合
     * @param item                   检测项目名称
     * @return 检测项目优先级
     */
    private static int getItemPriority(
            List<ItemPriorityForSort> commonItemPriorityList, String item) {
        ItemPriorityForSort itemPriorityForSort = commonItemPriorityList.stream()
                .filter(p -> StrUtil.startWith(item, p.getItem()))
                .findFirst()
                .orElse(null);
        return itemPriorityForSort != null ? itemPriorityForSort.getPriority() : Integer.MAX_VALUE;
    }

    /**
     * 处理普通集团
     *
     * @param company
     * @param customerNumberList
     */
    private void handleNormalGroup(Company company, List<String> customerNumberList) {
        // 集团编号集合
        List<String> groupNumberList = new ArrayList<>();
        // 客户所属集团信息
        Group group = groupMapper.selectOne(
                new LambdaQueryWrapper<Group>().eq(Group::getGroupNumber, company.getGroupNumber()));
        // 客户集团是否存在随车质检单集团
        if (ObjUtil.isNotNull(group.getTransportReportGroupId())) {
            // 查询随车质检单集团所属集团
            List<Group> groupList = groupMapper.selectList(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getTransportReportGroupId, group.getTransportReportGroupId()));
            // 添加集团编号至集团编号集合
            groupNumberList.addAll(
                    groupList.stream().map(Group::getGroupNumber).collect(Collectors.toList()));

            // 优化：查找transportReportGroupId相同的TransportReportGroup，并将关联的公司也添加到customerNumberList
            List<TransportReportGroup> transportReportGroups = transportReportGroupMapper.selectList(
                    new LambdaQueryWrapper<TransportReportGroup>()
                            .eq(TransportReportGroup::getTransportReportGroupId, group.getTransportReportGroupId()));

            if (!transportReportGroups.isEmpty()) {
                List<Integer> transportGroupIds = transportReportGroups.stream()
                        .map(TransportReportGroup::getId)
                        .collect(Collectors.toList());

                // 查询这些TransportReportGroup关联的所有TransportReportCompany
                List<TransportReportCompany> transportReportCompanies = transportReportCompanyMapper.selectList(
                        new LambdaQueryWrapper<TransportReportCompany>()
                                .in(TransportReportCompany::getGroupId, transportGroupIds));

                // 将这些公司的编号添加到customerNumberList
                customerNumberList.addAll(
                        transportReportCompanies.stream()
                                .map(TransportReportCompany::getCompanyNumber)
                                .collect(Collectors.toList()));
            }
        } else {
            // 添加集团编号至集团编号集合
            groupNumberList.add(group.getGroupNumber());
        }
        // 查询随车质检单集团客户信息
        List<Company> companyList = companyMapper.selectList(
                new LambdaQueryWrapper<Company>().in(Company::getGroupNumber, groupNumberList));
        // 添加客户编号至客户编号集合
        customerNumberList.addAll(
                companyList.stream().map(Company::getCompanyNumber).collect(Collectors.toList()));
    }

    /**
     * 处理运输报告集团
     *
     * @param transportReportCompany
     * @param customerNumberList
     */
    private void handleTransportReportGroup(TransportReportCompany transportReportCompany,
            List<String> customerNumberList) {
        // 查询同组的所有公司
        List<TransportReportCompany> groupCompanies = transportReportCompanyMapper.selectList(
                new LambdaQueryWrapper<TransportReportCompany>()
                        .eq(TransportReportCompany::getGroupId, transportReportCompany.getGroupId()));

        // 添加同组所有公司的编号到客户编号集合
        customerNumberList.addAll(
                groupCompanies.stream()
                        .map(TransportReportCompany::getCompanyNumber)
                        .collect(Collectors.toList()));

        // 优化：查找transportReportCompany所属的TransportReportGroup
        TransportReportGroup reportGroup = transportReportGroupMapper.selectOne(
                new LambdaQueryWrapper<TransportReportGroup>()
                        .eq(TransportReportGroup::getId, transportReportCompany.getGroupId()));

        if (reportGroup != null && reportGroup.getTransportReportGroupId() != null) {
            // 1. 查找具有相同transportReportGroupId的其他TransportReportGroup
            List<TransportReportGroup> sameIdGroups = transportReportGroupMapper.selectList(
                    new LambdaQueryWrapper<TransportReportGroup>()
                            .eq(TransportReportGroup::getTransportReportGroupId,
                                    reportGroup.getTransportReportGroupId())
                            .ne(TransportReportGroup::getId, reportGroup.getId())); // 排除当前group

            if (!sameIdGroups.isEmpty()) {
                List<Integer> otherGroupIds = sameIdGroups.stream()
                        .map(TransportReportGroup::getId)
                        .collect(Collectors.toList());

                // 查询这些TransportReportGroup关联的所有TransportReportCompany
                List<TransportReportCompany> otherCompanies = transportReportCompanyMapper.selectList(
                        new LambdaQueryWrapper<TransportReportCompany>()
                                .in(TransportReportCompany::getGroupId, otherGroupIds));

                // 将这些公司的编号添加到customerNumberList
                customerNumberList.addAll(
                        otherCompanies.stream()
                                .map(TransportReportCompany::getCompanyNumber)
                                .collect(Collectors.toList()));
            }

            // 2. 查询相同transportReportGroupId的Group
            List<Group> groups = groupMapper.selectList(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getTransportReportGroupId, reportGroup.getTransportReportGroupId()));

            if (!groups.isEmpty()) {
                List<String> groupNumbers = groups.stream()
                        .map(Group::getGroupNumber)
                        .collect(Collectors.toList());

                // 查询这些Group关联的所有Company
                List<Company> companies = companyMapper.selectList(
                        new LambdaQueryWrapper<Company>()
                                .in(Company::getGroupNumber, groupNumbers));

                // 将这些公司的编号添加到customerNumberList
                customerNumberList.addAll(
                        companies.stream()
                                .map(Company::getCompanyNumber)
                                .collect(Collectors.toList()));
            }
        }
    }

    /**
     * 获取集团和公司备注信息
     *
     * @param customerNumber 客户编号
     * @return Map 包含 groupRemarks 和 companyRemarks
     */
    private Map<String, String> getRemarks(String customerNumber) {
        Map<String, String> remarksMap = new HashMap<>();

        // 先查询 Company
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (ObjUtil.isNotNull(company)) {
            // 如果找到 Company，查询对应的 Group
            Group group = groupMapper.selectOne(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getGroupNumber, company.getGroupNumber()));

            // 设置集团备注
            if (ObjUtil.isNotNull(group)) {
                remarksMap.put("groupRemarks", group.getTransportReportRemark());
            }

            // 设置公司备注
            remarksMap.put("companyRemarks", company.getTransportReportRemark());
        } else {
            // 如果没找到 Company，查询 TransportReportCompany
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, customerNumber));

            if (ObjUtil.isNotNull(transportReportCompany)) {
                // 如果找到 TransportReportCompany，查询对应的 TransportReportGroup
                TransportReportGroup transportReportGroup = transportReportGroupMapper.selectOne(
                        new LambdaQueryWrapper<TransportReportGroup>()
                                .eq(TransportReportGroup::getId, transportReportCompany.getGroupId()));

                // 设置集团备注
                if (ObjUtil.isNotNull(transportReportGroup)) {
                    remarksMap.put("groupRemarks", transportReportGroup.getRemark());
                }

                // 设置公司备注
                remarksMap.put("companyRemarks", transportReportCompany.getRemark());
            }
        }

        return remarksMap;
    }

    
    /**
     * 将Excel列名（如A, B, AA, AB等）转换为数字索引（A=1, B=2, AA=27, AB=28等）
     * 
     * @param columnName Excel列名
     * @return 列索引（从1开始）
     */
    private int parseColumnNameToIndex(String columnName) {
        if (columnName == null || columnName.isEmpty()) {
            return 1;
        }
        
        int result = 0;
        for (int i = 0; i < columnName.length(); i++) {
            char c = columnName.charAt(i);
            result = result * 26 + (c - 'A' + 1);
        }
        return result;
    }

    /**
     * 截取前n个非标点符号的字符
     *
     * @param str    字符串
     * @param length 长度
     * @return 截取后的字符串
     */
    private String subPreIgnorePunctuation(String str, int length) {
        if (StrUtil.isBlank(str)) {
            return str;
        }

        StringBuilder sb = new StringBuilder();
        int count = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            sb.append(c);
            // 如果不是标点符号才计数
            if (!Character.isLetterOrDigit(c) && !Character.isIdeographic(c)) {
                continue;
            }
            count++;
            if (count >= length) {
                break;
            }
        }
        return sb.toString();
    }

    /**
     * 处理模板字段，包括获取通用字段和处理客户排除字段
     *
     * @param transportReportTemplate 模板对象
     * @param transferRecord          出库传递单记录
     * @return 处理后的模板字段集合
     */
    private Set<String> processTemplateFields(TransportReportTemplate transportReportTemplate,
            OutboundInformationTransferRecord transferRecord) {
        // 获取模板通用字段
        Set<String> templateFieldSet = CollUtil.newHashSet(transportReportTemplate.getTemplateField().split(","));

        // 处理客户排除字段
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, transferRecord.getCustomerNumber()));
        if (ObjUtil.isNotNull(company) && StrUtil.isNotBlank(company.getTransportReportExcludeFields())) {
            List<String> excludeFields = StrUtil.split(company.getTransportReportExcludeFields(), ',');
            templateFieldSet.removeAll(excludeFields);
        } else {
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, transferRecord.getCustomerNumber()));
            if (ObjUtil.isNotNull(transportReportCompany)
                    && StrUtil.isNotBlank(transportReportCompany.getTransportReportExcludeFields())) {
                List<String> excludeFields = StrUtil.split(transportReportCompany.getTransportReportExcludeFields(),
                        ',');
                templateFieldSet.removeAll(excludeFields);
            }
        }
        return templateFieldSet;
    }

    /**
     * 根据客户编号查询检测方法列表
     *
     * @param customerNumber        客户编号
     * @param productCategoryNumber 产品类别编号
     * @return 检测方法列表
     */
    private List<TransportReportItemTestMethod> getTestMethodsByCustomerNumber(String customerNumber,
            String productCategoryNumber) {
        List<TransportReportItemTestMethod> testMethodList = new ArrayList<>();

        // 首先查询客户是否在Company表中
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (company != null) {
            // 客户在Company表中，查询对应的Group
            Group group = groupMapper.selectOne(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getGroupNumber, company.getGroupNumber()));

            if (group != null && Boolean.TRUE.equals(group.getIsAddTestMethod())) {
                // 集团需要添加检测方法，查询检测方法列表
                testMethodList = transportReportItemTestMethodMapper.selectList(
                        new LambdaQueryWrapper<TransportReportItemTestMethod>()
                                .eq(TransportReportItemTestMethod::getGroupNumber, group.getGroupNumber())
                                .eq(TransportReportItemTestMethod::getProductCategoryNumber, productCategoryNumber));
                if (CollUtil.isEmpty(testMethodList)) {
                    testMethodList = transportReportItemTestMethodMapper.selectList(
                            new LambdaQueryWrapper<TransportReportItemTestMethod>()
                                    .isNull(TransportReportItemTestMethod::getGroupNumber)
                                    .isNull(TransportReportItemTestMethod::getGroupId)
                                    .eq(TransportReportItemTestMethod::getProductCategoryNumber,
                                            productCategoryNumber));
                }
            }
        } else {
            // 客户不在Company表中，查询TransportReportCompany表
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, customerNumber));

            if (transportReportCompany != null) {
                // 查询对应的TransportReportGroup
                TransportReportGroup transportReportGroup = transportReportGroupMapper.selectOne(
                        new LambdaQueryWrapper<TransportReportGroup>()
                                .eq(TransportReportGroup::getId, transportReportCompany.getGroupId()));

                if (transportReportGroup != null && Boolean.TRUE.equals(transportReportGroup.getIsAddTestMethod())) {
                    // 集团需要添加检测方法，查询检测方法列表
                    testMethodList = transportReportItemTestMethodMapper.selectList(
                            new LambdaQueryWrapper<TransportReportItemTestMethod>()
                                    .eq(TransportReportItemTestMethod::getGroupId, transportReportGroup.getId())
                                    .eq(TransportReportItemTestMethod::getProductCategoryNumber,
                                            productCategoryNumber));
                    if (CollUtil.isEmpty(testMethodList)) {
                        testMethodList = transportReportItemTestMethodMapper.selectList(
                                new LambdaQueryWrapper<TransportReportItemTestMethod>()
                                        .isNull(TransportReportItemTestMethod::getGroupNumber)
                                        .isNull(TransportReportItemTestMethod::getGroupId)
                                        .eq(TransportReportItemTestMethod::getProductCategoryNumber,
                                                productCategoryNumber));
                    }
                }
            }
        }

        // 如果testMethodList不为空，则查询产品类别为null的记录并合并
        if (CollUtil.isNotEmpty(testMethodList)) {
            // 查询产品类别为null的所有记录
            List<TransportReportItemTestMethod> nullCategoryMethodList = transportReportItemTestMethodMapper.selectList(
                    new LambdaQueryWrapper<TransportReportItemTestMethod>()
                            .isNull(TransportReportItemTestMethod::getProductCategoryNumber));

            if (CollUtil.isNotEmpty(nullCategoryMethodList)) {
                // 构建一个Map，key为itemName，value为testMethodList中对应的记录
                Map<String, TransportReportItemTestMethod> methodMap = testMethodList.stream()
                        .collect(Collectors.toMap(TransportReportItemTestMethod::getItemName, item -> item,
                                (k1, k2) -> k1));

                // 遍历nullCategoryMethodList，如果testMethodList中不存在相同itemName的记录，则添加到结果集中
                for (TransportReportItemTestMethod nullCategoryMethod : nullCategoryMethodList) {
                    if (!methodMap.containsKey(nullCategoryMethod.getItemName())) {
                        testMethodList.add(nullCategoryMethod);
                    }
                    // 如果已存在相同itemName的记录，则保留testMethodList中的记录（有类别编号的优先级更高）
                }
            }
        }

        return testMethodList;
    }

    /**
     * 根据客户编号获取报告配置项
     *
     * @param customerNumber 客户编号
     * @return 包含报告配置的Map，key为"isAddTankNumber"、"isAddInspectionDate"、"isAddCustomerProductCode"和按语言类型的"isSeparateReportPerBatch"
     */
    private Map<String, Boolean> getCustomerReportConfig(String customerNumber) {
        Map<String, Boolean> resultMap = new HashMap<>();
        // 默认不添加
        resultMap.put("isAddTankNumber", false);
        resultMap.put("isAddInspectionDate", false);
        resultMap.put("isAddCustomerProductCode", false);
        
        // 默认按语言类型设置为false
        resultMap.put("isSeparateReportPerBatch_1", false); // 中文
        resultMap.put("isSeparateReportPerBatch_2", false); // 英文
        resultMap.put("isSeparateReportPerBatch_3", false); // 中英文

        // 首先查询客户是否在Company表中
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (company != null) {
            // 客户在Company表中，查询对应的Group
            Group group = groupMapper.selectOne(
                    new LambdaQueryWrapper<Group>()
                            .eq(Group::getGroupNumber, company.getGroupNumber()));

            if (group != null) {
                // 获取集团的配置
                resultMap.put("isAddTankNumber", Boolean.TRUE.equals(group.getIsAddTankNumber()));
                resultMap.put("isAddInspectionDate", Boolean.TRUE.equals(group.getIsAddInspectionDate()));
                resultMap.put("isAddCustomerProductCode", Boolean.TRUE.equals(group.getIsAddCustomerProductCode()));
                
                // 处理按语言类型的配置
                handleLanguageSpecificConfig(resultMap, group.getIsSeparateReportPerBatch());
            }

            // 检查公司级别是否有单独设置，公司设置优先级高于集团设置
            if (company.getIsSeparateReportPerBatch() != null) {
                handleLanguageSpecificConfig(resultMap, company.getIsSeparateReportPerBatch());
            }
        } else {
            // 客户不在Company表中，查询TransportReportCompany表
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, customerNumber));

            if (transportReportCompany != null) {
                // 查询对应的TransportReportGroup
                TransportReportGroup transportReportGroup = transportReportGroupMapper.selectOne(
                        new LambdaQueryWrapper<TransportReportGroup>()
                                .eq(TransportReportGroup::getId, transportReportCompany.getGroupId()));

                if (transportReportGroup != null) {
                    // 获取集团的配置
                    resultMap.put("isAddTankNumber", Boolean.TRUE.equals(transportReportGroup.getIsAddTankNumber()));
                    resultMap.put("isAddInspectionDate",
                            Boolean.TRUE.equals(transportReportGroup.getIsAddInspectionDate()));
                    resultMap.put("isAddCustomerProductCode",
                            Boolean.TRUE.equals(transportReportGroup.getIsAddCustomerProductCode()));
                    
                    // 处理按语言类型的配置
                    handleLanguageSpecificConfig(resultMap, transportReportGroup.getIsSeparateReportPerBatch());
                }

                // 检查公司级别是否有单独设置，公司设置优先级高于集团设置
                if (transportReportCompany.getIsSeparateReportPerBatch() != null) {
                    handleLanguageSpecificConfig(resultMap, transportReportCompany.getIsSeparateReportPerBatch());
                }
            }
        }

        return resultMap;
    }

    /**
     * 处理按语言类型的配置
     * 
     * @param resultMap 结果Map
     * @param configValue 配置值，可能是Boolean（向后兼容）或String（JSON格式）
     */
    private void handleLanguageSpecificConfig(Map<String, Boolean> resultMap, Object configValue) {
        if (configValue == null) {
            return;
        }
        
        if (configValue instanceof Boolean) {
            // 向后兼容：如果是Boolean类型，则对所有语言类型都应用同样的设置
            Boolean boolValue = (Boolean) configValue;
            resultMap.put("isSeparateReportPerBatch_1", boolValue); // 中文
            resultMap.put("isSeparateReportPerBatch_2", boolValue); // 英文
            resultMap.put("isSeparateReportPerBatch_3", boolValue); // 中英文
        } else if (configValue instanceof String) {
            // 新格式：JSON字符串
            try {
                String jsonStr = (String) configValue;
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Boolean> languageConfig = objectMapper.readValue(jsonStr, 
                    new TypeReference<Map<String, Boolean>>() {});
                
                // 更新各语言类型的配置
                if (languageConfig.containsKey("1")) {
                    resultMap.put("isSeparateReportPerBatch_1", languageConfig.get("1"));
                }
                if (languageConfig.containsKey("2")) {
                    resultMap.put("isSeparateReportPerBatch_2", languageConfig.get("2"));
                }
                if (languageConfig.containsKey("3")) {
                    resultMap.put("isSeparateReportPerBatch_3", languageConfig.get("3"));
                }
            } catch (Exception e) {
                log.warn("解析语言类型配置失败: {}, 使用默认配置", configValue, e);
            }
        }
    }

    /**
     * 根据项目名称获取对应的检测方法
     *
     * @param itemString              项目名称
     * @param testMethodList          检测方法列表
     * @param saleOrderRequirementMap 销售订单要求映射
     * @return 检测方法文本
     */
    private String getTestMethodForItem(
            String itemString,
            List<TransportReportItemTestMethod> testMethodList,
            Map<String, Map<String, String>> saleOrderRequirementMap) {
        log.info("itemString: {}", itemString);

        // 预处理itemString，取逗号前的内容，再取括号前的内容
        String processedItemString = itemString.split(",")[0].split("\\(")[0].trim();
        log.info("processedItemString: {}", processedItemString);

        // 1. 在saleOrderRequirementMap中找到itemName以processedItemString开头的map
        Optional<Map.Entry<String, Map<String, String>>> itemEntry = saleOrderRequirementMap.entrySet().stream()
                .filter(entry -> {
                    Map<String, String> valueMap = entry.getValue();
                    return valueMap.containsKey("itemName") &&
                            valueMap.get("itemName").startsWith(processedItemString);
                })
                .findFirst();

        String matchingName;
        if (!itemEntry.isPresent()) {
            matchingName = processedItemString;
            log.info("未找到匹配项，使用processedItemString作为matchingName: {}", matchingName);
        } else {
            // 2. 获取matchingName值
            matchingName = itemEntry.get().getValue().get("matchingName").split(",")[0].split("\\(")[0];
            log.info("rawMatchingName: {}", itemEntry.get().getValue().get("matchingName"));
            log.info("matchingName: {}", matchingName);
        }

        if (StrUtil.isBlank(matchingName)) {
            return "";
        }

        // 3. 在testMethodList中查找匹配的项
        return testMethodList.stream()
                .filter(method -> matchingName.equals(method.getItemName()))
                .map(TransportReportItemTestMethod::getTestMethod)
                .findFirst()
                .orElse("");
    }

    /**
     * 根据检测数据列表大小、质检要求列表大小、是否添加检测方法和客户编号获取合适的运输报告模板列表
     *
     * @param transportReportDataSize 检测数据列表大小
     * @param requirementListSize     质检要求列表大小
     * @param addTestMethod           是否添加检测方法
     * @param customerNumber          客户编号
     * @return 运输报告模板列表
     */
    private List<TransportReportTemplate> getTransportReportTemplates(
            int transportReportDataSize,
            int requirementListSize,
            boolean addTestMethod,
            String customerNumber) {

        // 1. 先查询通用模板作为基础
        List<TransportReportTemplate> transportReportTemplateList;

        if (transportReportDataSize <= 6) {
            transportReportTemplateList = transportReportTemplateMapper.selectList(
                    new LambdaQueryWrapper<TransportReportTemplate>()
                            .eq(requirementListSize > 14, TransportReportTemplate::getItemNumber, 14)
                            .eq(requirementListSize <= 14, TransportReportTemplate::getItemNumber, requirementListSize)
                            .eq(TransportReportTemplate::getTemplateType, 1)
                            .eq(TransportReportTemplate::getIsAddTestMethod, addTestMethod)
                            .isNull(TransportReportTemplate::getGroupId)
                            .isNull(TransportReportTemplate::getGroupNumber)
                            .isNull(TransportReportTemplate::getCustomerNumber));
        } else {
            transportReportTemplateList = transportReportTemplateMapper.selectList(
                    new LambdaQueryWrapper<TransportReportTemplate>()
                            .eq(TransportReportTemplate::getItemNumber, 10)
                            .eq(TransportReportTemplate::getTemplateType, 2)
                            .eq(TransportReportTemplate::getIsAddTestMethod, false)
                            .isNull(TransportReportTemplate::getGroupId)
                            .isNull(TransportReportTemplate::getGroupNumber)
                            .isNull(TransportReportTemplate::getCustomerNumber));
        }

        // 根据通用模板的languageType分组，作为最低优先级的基础
        Map<Integer, TransportReportTemplate> finalTemplateMap = transportReportTemplateList.stream()
                .collect(Collectors.toMap(
                        TransportReportTemplate::getLanguageType,
                        template -> template,
                        (existing, replacement) -> existing));

        // 2. 查询客户特定模板（无论是否找到都继续查询集团模板）
        List<TransportReportTemplate> customerTemplates = new ArrayList<>();

        // 2.1 首先根据客户编号和requirementListSize查询
        customerTemplates = transportReportTemplateMapper.selectList(
                new LambdaQueryWrapper<TransportReportTemplate>()
                        .eq(TransportReportTemplate::getCustomerNumber, customerNumber)
                        .eq(requirementListSize > 14, TransportReportTemplate::getItemNumber, 14)
                        .eq(requirementListSize <= 14, TransportReportTemplate::getItemNumber, requirementListSize));

        // 2.2 如果没有找到合适的模板，再按照客户编号查询所有模板
        if (CollUtil.isEmpty(customerTemplates)) {
            customerTemplates = transportReportTemplateMapper.selectList(
                    new LambdaQueryWrapper<TransportReportTemplate>()
                            .eq(TransportReportTemplate::getCustomerNumber, customerNumber));
        }

        // 3. 查询集团模板（独立于客户模板查询，确保集团模板能够补充客户模板）
        List<TransportReportTemplate> groupTemplates = new ArrayList<>();

        // 3.1 首先查询客户是否在Company表中
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (company != null) {
            // 3.2 使用集团编号查询集团模板
            String groupNumber = company.getGroupNumber();
            if (StrUtil.isNotBlank(groupNumber)) {
                // 先使用集团编号和requirementListSize查询模板
                groupTemplates = transportReportTemplateMapper.selectList(
                        new LambdaQueryWrapper<TransportReportTemplate>()
                                .eq(TransportReportTemplate::getGroupNumber, groupNumber)
                                .eq(requirementListSize > 14, TransportReportTemplate::getItemNumber, 14)
                                .eq(requirementListSize <= 14, TransportReportTemplate::getItemNumber,
                                        requirementListSize));

                // 如果没有找到合适的模板，再按照集团编号查询所有模板
                if (CollUtil.isEmpty(groupTemplates)) {
                    groupTemplates = transportReportTemplateMapper.selectList(
                            new LambdaQueryWrapper<TransportReportTemplate>()
                                    .eq(TransportReportTemplate::getGroupNumber, groupNumber));
                }
            }
        } else {
            // 3.3 客户不在Company表中，查询TransportReportCompany表
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, customerNumber));

            if (transportReportCompany != null && transportReportCompany.getGroupId() != null) {
                // 先使用集团ID和requirementListSize查询模板
                groupTemplates = transportReportTemplateMapper.selectList(
                        new LambdaQueryWrapper<TransportReportTemplate>()
                                .eq(TransportReportTemplate::getGroupId, transportReportCompany.getGroupId())
                                .eq(requirementListSize > 14, TransportReportTemplate::getItemNumber, 14)
                                .eq(requirementListSize <= 14, TransportReportTemplate::getItemNumber,
                                        requirementListSize));

                // 如果没有找到合适的模板，再按照集团ID查询所有模板
                if (CollUtil.isEmpty(groupTemplates)) {
                    groupTemplates = transportReportTemplateMapper.selectList(
                            new LambdaQueryWrapper<TransportReportTemplate>()
                                    .eq(TransportReportTemplate::getGroupId, transportReportCompany.getGroupId()));
                }
            }
        }

        // 4. 按优先级合并模板：客户模板 > 集团模板 > 通用模板
        // 4.1 先将集团模板按languageType合并到最终结果中（集团模板优先于通用模板）
        if (CollUtil.isNotEmpty(groupTemplates)) {
            Map<Integer, TransportReportTemplate> groupTemplateMap = groupTemplates.stream()
                    .collect(Collectors.toMap(
                            TransportReportTemplate::getLanguageType,
                            template -> template,
                            (existing, replacement) -> existing));

            // 集团模板覆盖通用模板
            groupTemplateMap.forEach((languageType, template) -> finalTemplateMap.put(languageType, template));
        }

        // 4.2 再将客户模板按languageType合并到最终结果中（客户模板优先级最高）
        if (CollUtil.isNotEmpty(customerTemplates)) {
            Map<Integer, TransportReportTemplate> customerTemplateMap = customerTemplates.stream()
                    .collect(Collectors.toMap(
                            TransportReportTemplate::getLanguageType,
                            template -> template,
                            (existing, replacement) -> existing));

            // 客户模板覆盖集团模板和通用模板
            customerTemplateMap.forEach((languageType, template) -> finalTemplateMap.put(languageType, template));
        }

        // 5. 将最终的Map转换回List返回
        return new ArrayList<>(finalTemplateMap.values());
    }

    /**
     * 获取指标接收日期
     *
     * @param customerNumber        客户编号
     * @param productNumber         产品编号
     * @param productCategoryNumber 产品类别编号
     * @return 指标接收日期
     */
    private String getReceiptDate(String customerNumber, String productNumber, String productCategoryNumber) {
        if (StrUtil.isBlank(customerNumber)
                || (StrUtil.isBlank(productNumber) && StrUtil.isBlank(productCategoryNumber))) {
            return "";
        }

        // 根据客户编号查询集团编号
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (company == null || StrUtil.isBlank(company.getGroupNumber())) {
            return "";
        }

        String groupNumber = company.getGroupNumber();

        // 根据集团编号和产品编号查询
        List<GroupQualityStandard> standards = new ArrayList<>();
        if (StrUtil.isNotBlank(productNumber)) {
            standards = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, groupNumber)
                            .eq(GroupQualityStandard::getProductNumber, productNumber));
        }

        // 如果没查到结果，则使用产品类别编号再次查询
        if (CollUtil.isEmpty(standards) && StrUtil.isNotBlank(productCategoryNumber)) {
            standards = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, groupNumber)
                            .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryNumber));
        }

        // 取结果的第一条记录的receiptDate
        if (CollUtil.isNotEmpty(standards)) {
            return standards.get(0).getReceiptDate();
        }

        return "";
    }

    /**
     * 处理客户自定义字段内容，并将其填充到Excel工作表中。
     * <p>
     * 此方法按优先级顺序（公司特定设置、集团特定设置、模板默认设置）处理最多8个客户自定义字段。
     * 优先级顺序如下：
     * <ol>
     * <li>公司级别 (实体 {@code Company} 或 {@code TransportReportCompany})</li>
     * <li>集团级别 (实体 {@code Group} 或 {@code TransportReportGroup})</li>
     * <li>模板级别 (参数 {@code transportReportTemplate})</li>
     * </ol>
     * <p>
     * 字段处理规则：
     * <ul>
     * <li>如果一个字段名称 (例如 "客户产品代码") 已被处理，则后续遇到同名字段（即使在不同位置）将被忽略。</li>
     * <li>如果一个单元格位置 (行, 列) 已被填充，则后续尝试填充该位置（即使使用不同字段名）将被忽略。</li>
     * </ul>
     * <p>
     * 方法会根据字段名称（如 "英文产品名称", "客户产品代码" 等）应用特定的逻辑来获取和格式化单元格内容。
     *
     * @param sheet                   工作表对象，用于获取和设置单元格内容。
     * @param transportReportTemplate 运输报告模板对象，包含客户自定义字段的默认配置。
     * @param saleOrderRequirementMap 销售订单要求映射，用于查找某些字段的默认值 (如 "客户产品代码", "保质期")。
     * @param configMap               配置映射，包含如是否添加客户产品代码等布尔标志。
     * @param templateFieldSet        一个集合，用于跟踪已在主报告中处理的字段名，以避免重复处理。此方法会从中移除已处理的客户特定字段（如果它们也属于标准报告项）。
     * @param transferRecord          出库传递单记录，包含客户、产品等信息。
     * @param transportReport         运输报告对象，包含如报告日期等信息。
     * @param i18nMap                 国际化翻译映射，用于字段内容的本地化。
     * @return 返回一个列表，包含实际被处理并添加到工作表中的客户自定义字段的名称。
     */
    private List<String> processCustomerFields(
            Worksheet sheet,
            TransportReportTemplate transportReportTemplate,
            Map<String, Map<String, String>> saleOrderRequirementMap,
            Map<String, Boolean> configMap,
            Set<String> templateFieldSet,
            OutboundInformationTransferRecord transferRecord,
            TransportQualityInspectionReport transportReport,
            Map<String, Map<String, String>> i18nMap) {
        List<String> customerItemList = new ArrayList<>();
        // 用于跟踪已处理的字段名，避免重复处理同一个字段
        Set<String> processedFieldNames = new HashSet<>();
        // 用于跟踪已填充的单元格位置（格式："行_列"），避免在同一位置重复写入
        Set<String> processedPositions = new HashSet<>();

        // 步骤 A: 判断当前模板是否为"通用"模板。
        // 通用模板是指其自身属性未指定任何特定集团或客户的模板。
        boolean isCommonTemplate = transportReportTemplate.getGroupId() == null &&
                                   StrUtil.isBlank(transportReportTemplate.getGroupNumber()) &&
                                   StrUtil.isBlank(transportReportTemplate.getCustomerNumber());

        // 步骤 B: 根据模板类型和优先级构建配置源列表。
        // - 如果是通用模板：公司/集团配置优先，然后是模板自身的配置。
        // - 如果是特定模板：仅应用模板自身的配置。
        List<Object> sources = new ArrayList<>();

        if (isCommonTemplate) {
            // 这是一个通用模板：加载公司和集团配置。
            // 这些配置将在模板本身之前添加到"sources"列表中，从而确立优先级。
            Company company = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, transferRecord.getCustomerNumber()));
            TransportReportCompany transportReportCompany = null;
            if (company == null) {
                transportReportCompany = transportReportCompanyMapper.selectOne(
                        new LambdaQueryWrapper<TransportReportCompany>()
                                .eq(TransportReportCompany::getCompanyNumber, transferRecord.getCustomerNumber()));
            }

            Group group = null;
            if (company != null && StrUtil.isNotBlank(company.getGroupNumber())) {
                group = groupMapper.selectOne(
                        new LambdaQueryWrapper<Group>().eq(Group::getGroupNumber, company.getGroupNumber()));
            }

            TransportReportGroup transportReportGroup = null;
            if (transportReportCompany != null && transportReportCompany.getGroupId() != null) {
                transportReportGroup = transportReportGroupMapper.selectOne(
                        new LambdaQueryWrapper<TransportReportGroup>()
                                .eq(TransportReportGroup::getId, transportReportCompany.getGroupId()));
            }

            // 按优先级顺序（公司 > 集团）将公司/集团配置源添加到列表
            if (company != null) {
                sources.add(company);
            }
            if (transportReportCompany != null) {
                sources.add(transportReportCompany);
            }
            if (group != null) {
                sources.add(group);
            }
            if (transportReportGroup != null) {
                sources.add(transportReportGroup);
            }
        }

        // 模板本身始终是一个配置源。
        // 对于通用模板，它是备用选项。对于特定模板，它是唯一的配置源。
        sources.add(transportReportTemplate);

        // 步骤 C: 遍历自定义字段槽位（1到8）并应用配置。
        for (int fieldIndex = 1; fieldIndex <= 8; fieldIndex++) {
            Integer bestRow = null;
            Integer bestCol = null;
            String bestItemName = null;

            // 3.1. 在所有配置源中查找当前字段槽位的定义，遵循优先级
            for (Object currentSource : sources) {
                try {
                    // 通过反射获取当前源中对应槽位的行、列、项目名称
                    Field rowField = currentSource.getClass().getDeclaredField("customerRow" + fieldIndex);
                    Field colField = currentSource.getClass().getDeclaredField("customerColumn" + fieldIndex);
                    Field itemField = currentSource.getClass().getDeclaredField("customerItem" + fieldIndex);

                    rowField.setAccessible(true);
                    colField.setAccessible(true);
                    itemField.setAccessible(true);

                    Integer r = (Integer) rowField.get(currentSource);
                    Integer c = (Integer) colField.get(currentSource);
                    String item = (String) itemField.get(currentSource);

                    // 如果当前源定义了此槽位 (行、列、项目名均有效)
                    if (r != null && c != null && StrUtil.isNotBlank(item)) {
                        String positionKey = r + "_" + c;

                        // 检查冲突：
                        // - 如果此字段名 (item) 已被处理过，则跳过。
                        // - 如果此单元格位置 (positionKey) 已被填充过，则跳过。
                        if (processedFieldNames.contains(item) || processedPositions.contains(positionKey)) {
                            continue; // 跳过此源，尝试下一个更高优先级的源（对于此fieldIndex）或已处理
                        }

                        // 无冲突，此定义有效且为当前fieldIndex找到的最高优先级定义
                        bestRow = r;
                        bestCol = c;
                        bestItemName = item;
                        break; // 已找到此fieldIndex的最佳定义，跳出sources循环，进行处理
                    }
                } catch (NoSuchFieldException nsfe) {
                    // 预期异常：某些配置源可能未定义全部8个customerRow/Col/Item字段
                } catch (Exception e) {
                    log.error("处理客户自定义字段 {} (源: {}) 时反射出错: {}",
                            fieldIndex, currentSource.getClass().getSimpleName(), e.getMessage(), e);
                }
            }

            // 3.2. 如果找到了有效的、无冲突的字段定义，则处理并填充到Excel
            if (bestRow != null && bestCol != null && StrUtil.isNotBlank(bestItemName)) {
                CellRange customCell = sheet.getCellRange(bestRow, bestCol);
                String cellContent = ""; // 初始化单元格内容

                // 根据 bestItemName (字段项目名) 决定单元格内容
                switch (bestItemName) {
                    case "英文产品名称":
                        if (i18nMap.containsKey(transferRecord.getProductName()) &&
                                i18nMap.get(transferRecord.getProductName()).containsKey("enUsOptimized")) {
                            cellContent = i18nMap.get(transferRecord.getProductName()).get("enUsOptimized");
                        } else if (i18nMap.containsKey(transferRecord.getProductName()) &&
                                i18nMap.get(transferRecord.getProductName()).containsKey("enUs")) {
                            cellContent = i18nMap.get(transferRecord.getProductName()).get("enUs");
                        } else {
                            // 如果没有优化翻译或标准翻译，则回退到产品名称本身
                            cellContent = transferRecord.getProductName();
                        }
                        break;
                    case "客户产品代码":
                        if (saleOrderRequirementMap.containsKey("客户产品代码") &&
                                saleOrderRequirementMap.get("客户产品代码").containsKey("requirement")) {
                            cellContent = saleOrderRequirementMap.get("客户产品代码").get("requirement");
                        }
                        // configMap中的isAddCustomerProductCode标志将在方法末尾根据是否实际处理此项来更新
                        break;
                    case "指标接收日期":
                        String receiptDate = getReceiptDate(
                                transferRecord.getCustomerNumber(),
                                transferRecord.getProductNumber(),
                                transferRecord.getProductCategoryNumber());
                        if (StrUtil.isNotBlank(receiptDate)) {
                            cellContent = receiptDate;
                        }
                        break;
                    case "发运日期":
                        cellContent = transportReport.getReportDate();
                        break;
                    case "保质期": // 此case也隐式处理"有效期"，基于saleOrderRequirementMap的检查顺序
                        if (saleOrderRequirementMap.containsKey("保质期") &&
                                saleOrderRequirementMap.get("保质期").containsKey("requirement")) {
                            cellContent = saleOrderRequirementMap.get("保质期").get("requirement");
                        } else if (saleOrderRequirementMap.containsKey("有效期") &&
                                saleOrderRequirementMap.get("有效期").containsKey("requirement")) {
                            // 如果没有"保质期"，则尝试使用"有效期"
                            cellContent = saleOrderRequirementMap.get("有效期").get("requirement");
                        }
                        break;
                    case "红外光谱": // 此case也隐式处理"红外光谱相似度"
                        if (saleOrderRequirementMap.containsKey("红外光谱") &&
                                saleOrderRequirementMap.get("红外光谱").containsKey("requirement")) {
                            cellContent = saleOrderRequirementMap.get("红外光谱").get("requirement");
                        } else if (saleOrderRequirementMap.containsKey("红外光谱相似度") &&
                                saleOrderRequirementMap.get("红外光谱相似度").containsKey("requirement")) {
                            // 如果没有"红外光谱"，则尝试使用"红外光谱相似度"
                            cellContent = saleOrderRequirementMap.get("红外光谱相似度").get("requirement");
                        }
                        break;
                    default:
                        // 对于其他通用的自定义项目，尝试从销售订单要求中获取其内容
                        if (saleOrderRequirementMap.containsKey(bestItemName) &&
                                saleOrderRequirementMap.get(bestItemName).containsKey("requirement")) {
                            cellContent = saleOrderRequirementMap.get(bestItemName).get("requirement");
                        }
                        break;
                }

                // 填充单元格（允许填充空字符串，如果逻辑上需要）并记录已处理状态
                // 使用isNotBlank判断，确保只有在内容非空时才实际写入和记录，除非业务需要写入空值。
                // 此处保持与原逻辑一致，如果cellContent确定要写入，即使为空，也执行。
                // if (StrUtil.isNotBlank(cellContent)) { // 或者根据业务需求，允许写入空字符串
                customCell.setText(cellContent);
                customerItemList.add(bestItemName); // 将实际处理的项目名添加到返回列表

                // 标记此字段名和单元格位置已处理
                processedFieldNames.add(bestItemName);
                processedPositions.add(bestRow + "_" + bestCol);

                // 如果此客户字段名与标准报告项目名重合，则从templateFieldSet中移除，
                // 以避免在后续通用项目填充逻辑中重复处理。
                if (saleOrderRequirementMap.containsKey(bestItemName) ||
                        "保质期".equals(bestItemName) || "有效期".equals(bestItemName) ||
                        "红外光谱".equals(bestItemName) || "红外光谱相似度".equals(bestItemName) ||
                        "客户产品代码".equals(bestItemName)) {
                    templateFieldSet.remove(bestItemName);
                    // 特殊处理关联字段
                    if ("保质期".equals(bestItemName)) {
                        templateFieldSet.remove("有效期");
                        customerItemList.add("有效期");
                    }
                    if ("有效期".equals(bestItemName)) {
                        templateFieldSet.remove("保质期");
                        customerItemList.add("保质期");
                    }
                    if ("红外光谱".equals(bestItemName)) {
                        templateFieldSet.remove("红外光谱相似度");
                        customerItemList.add("红外光谱相似度");
                    }
                    if ("红外光谱相似度".equals(bestItemName)) {
                        templateFieldSet.remove("红外光谱");
                        customerItemList.add("红外光谱");
                    }
                }
            }
        }

        // 如果"客户产品代码"字段在循环中被成功处理了，则更新configMap
        if (processedFieldNames.contains("客户产品代码")) {
            configMap.put("isAddCustomerProductCode", false);
        }
        return customerItemList;
    }

    /**
     * 获取基础信息并更新质检要求映射
     *
     * @param transferRecord 出库传递单
     * @param requirementMap 销售订单质检要求映射
     */
    private void getBaseInfo(String customerNumber, String productNumber, String productCategoryNumber,
            Map<String, Map<String, String>> requirementMap, String requirementCategory) {
        // 查询是否有对应集团
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (company != null && StrUtil.isNotBlank(company.getGroupNumber())) {
            // 优先使用集团编号和产品编号查询
            List<GroupQualityStandard> groupQualityStandards = groupQualityStandardMapper.selectList(
                    new LambdaQueryWrapper<GroupQualityStandard>()
                            .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                            .eq(GroupQualityStandard::getProductNumber, productNumber)
                            .eq(StrUtil.isNotBlank(requirementCategory), GroupQualityStandard::getRequirementCategory,
                                    requirementCategory)
                            .in(GroupQualityStandard::getQualityInspectionItem,
                                    Arrays.asList("客户产品代码", "到期日", "有效期", "保质期")));

            // 如果没查询到，再使用集团编号和产品类别编号查询
            if (CollUtil.isEmpty(groupQualityStandards)
                    && StrUtil.isNotBlank(productCategoryNumber)) {
                groupQualityStandards = groupQualityStandardMapper.selectList(
                        new LambdaQueryWrapper<GroupQualityStandard>()
                                .eq(GroupQualityStandard::getGroupNumber, company.getGroupNumber())
                                .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryNumber)
                                .eq(StrUtil.isNotBlank(requirementCategory),
                                        GroupQualityStandard::getRequirementCategory,
                                        requirementCategory)
                                .in(GroupQualityStandard::getQualityInspectionItem,
                                        Arrays.asList("客户产品代码", "到期日", "有效期", "保质期")));
            }

            // 更新销售订单质检要求映射
            if (CollUtil.isNotEmpty(groupQualityStandards)) {
                requirementMap.remove("客户产品代码");
                requirementMap.remove("有效期");
                requirementMap.remove("保质期");
                requirementMap.remove("到期日");
                for (GroupQualityStandard standard : groupQualityStandards) {
                    Map<String, String> itemMap = new HashMap<>(4);
                    itemMap.put("matchingName", standard.getQualityInspectionItem());
                    itemMap.put("itemName", standard.getItemName());
                    String qualityRequirement = standard.getQualityRequirement();
                    if ("客户产品代码".equals(standard.getQualityInspectionItem()) && StrUtil.isNotBlank(qualityRequirement)
                            && StrUtil.isNotBlank(company.getCompanyCode())) {
                        qualityRequirement = qualityRequirement.replace("{companyCode}", company.getCompanyCode());
                    }
                    itemMap.put("requirement", qualityRequirement);
                    itemMap.put("showStatus", standard.getShowStatus().toString());
                    requirementMap.put(standard.getQualityInspectionItem(), itemMap);
                }
            }
        }
    }

    /**
     * 根据客户编号获取i18n翻译映射
     * <p>
     * 该方法按照优先级依次获取通用翻译、集团级翻译和公司级翻译，并将它们合并。
     * 优先级为：公司翻译 > 集团翻译 > 通用翻译
     * </p>
     *
     * @param customerNumber 客户编号
     * @return 合并后的翻译映射
     */
    private Map<String, Map<String, String>> getI18nMapByCustomerNumber(String customerNumber) {
        // 1. 首先获取通用翻译（集团id、集团编号、公司编号均为null的记录）
        Map<String, Map<String, String>> i18nMap = transportReportItemI18nMapper.selectList(
                new LambdaQueryWrapper<TransportReportItemI18n>()
                        .isNull(TransportReportItemI18n::getGroupId)
                        .isNull(TransportReportItemI18n::getGroupNumber)
                        .isNull(TransportReportItemI18n::getCompanyNumber))
                .stream()
                .collect(Collectors.toMap(
                        TransportReportItemI18n::getZhCn,
                        item -> {
                            Map<String, String> translations = new HashMap<>();
                            translations.put("zhCnOptimized", item.getZhCnOptimized());
                            translations.put("enUs", item.getEnUs());
                            translations.put("enUsOptimized", item.getEnUsOptimized());
                            return translations;
                        },
                        (existing, replacement) -> existing));

        // 2. 然后查询客户所属集团信息
        String groupNumber = null;
        Integer groupId = null;

        // 2.1 先查询普通公司表
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCompanyNumber, customerNumber));

        if (ObjUtil.isNotNull(company)) {
            // 从普通公司表获取集团编号
            groupNumber = company.getGroupNumber();

            // 查询该集团下的翻译记录
            if (StrUtil.isNotBlank(groupNumber)) {
                // 使用集团编号查询翻译记录
                Map<String, Map<String, String>> groupI18nMap = transportReportItemI18nMapper.selectList(
                        new LambdaQueryWrapper<TransportReportItemI18n>()
                                .eq(TransportReportItemI18n::getGroupNumber, groupNumber)
                                .isNull(TransportReportItemI18n::getCompanyNumber))
                        .stream()
                        .collect(Collectors.toMap(
                                TransportReportItemI18n::getZhCn,
                                item -> {
                                    Map<String, String> translations = new HashMap<>();
                                    translations.put("zhCnOptimized", item.getZhCnOptimized());
                                    translations.put("enUs", item.getEnUs());
                                    translations.put("enUsOptimized", item.getEnUsOptimized());
                                    return translations;
                                },
                                (existing, replacement) -> existing));

                // 集团翻译覆盖通用翻译
                i18nMap.putAll(groupI18nMap);
            }
        } else {
            // 2.2 查询运输报告公司表
            TransportReportCompany transportReportCompany = transportReportCompanyMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportCompany>()
                            .eq(TransportReportCompany::getCompanyNumber, customerNumber));

            if (ObjUtil.isNotNull(transportReportCompany)) {
                // 从运输报告公司表获取集团ID
                groupId = transportReportCompany.getGroupId();

                // 查询该集团下的翻译记录
                if (ObjUtil.isNotNull(groupId)) {
                    // 使用集团ID查询翻译记录
                    Map<String, Map<String, String>> groupI18nMap = transportReportItemI18nMapper.selectList(
                            new LambdaQueryWrapper<TransportReportItemI18n>()
                                    .eq(TransportReportItemI18n::getGroupId, groupId)
                                    .isNull(TransportReportItemI18n::getCompanyNumber))
                            .stream()
                            .collect(Collectors.toMap(
                                    TransportReportItemI18n::getZhCn,
                                    item -> {
                                        Map<String, String> translations = new HashMap<>();
                                        translations.put("zhCnOptimized", item.getZhCnOptimized());
                                        translations.put("enUs", item.getEnUs());
                                        translations.put("enUsOptimized", item.getEnUsOptimized());
                                        return translations;
                                    },
                                    (existing, replacement) -> existing));

                    // 集团翻译覆盖通用翻译
                    i18nMap.putAll(groupI18nMap);
                }
            }
        }

        // 3. 最后查询公司特定的翻译记录
        Map<String, Map<String, String>> companyI18nMap = transportReportItemI18nMapper.selectList(
                new LambdaQueryWrapper<TransportReportItemI18n>()
                        .eq(TransportReportItemI18n::getCompanyNumber, customerNumber))
                .stream()
                .collect(Collectors.toMap(
                        TransportReportItemI18n::getZhCn,
                        item -> {
                            Map<String, String> translations = new HashMap<>();
                            translations.put("zhCnOptimized", item.getZhCnOptimized());
                            translations.put("enUs", item.getEnUs());
                            return translations;
                        },
                        (existing, replacement) -> existing));

        // 公司翻译覆盖集团翻译和通用翻译
        i18nMap.putAll(companyI18nMap);

        return i18nMap;
    }

    /**
     * 根据公司、集团配置优先级设置执行标准和结论。
     * 优先级：公司 > 集团 > 默认（客户名称标准/结论）。
     * 新增逻辑：
     * 1. 如果forceApply为true，直接按配置覆盖，不判断客户指标
     * 2. 如果forceApply为false，先判断客户是否有指标，无指标时不覆盖，有指标时按配置覆盖
     * 3. 公司general=true时强制通用；否则有文本用文本。
     * 4. 公司无设置时，集团general=true时强制通用；否则有文本用文本。
     * 5. 都无设置时，若客户属于集团且有产品指标，则用客户名称标准/结论。
     */
    private void updateExecutionStandardAndConclusion(
            TransportQualityInspectionReport transportReport,
            String customerNumber,
            String productCategoryNumber) {
        String customerName = transportReport.getCustomerName();
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
        
        // 1. 公司优先判断
        if (company != null) {
            // 处理执行标准
            handleExecutionStandardForCompany(transportReport, company, customerNumber, productCategoryNumber, customerName);
            // 处理结论
            handleConclusionForCompany(transportReport, company, customerNumber, productCategoryNumber, customerName);
            return;
        }
        
        // 2. 运输报告公司判断
        TransportReportCompany trCompany = transportReportCompanyMapper.selectOne(
                new LambdaQueryWrapper<TransportReportCompany>().eq(TransportReportCompany::getCompanyNumber,
                        customerNumber));
        if (trCompany != null) {
            // 处理执行标准
            handleExecutionStandardForTransportReportCompany(transportReport, trCompany, customerNumber, productCategoryNumber, customerName);
            // 处理结论
            handleConclusionForTransportReportCompany(transportReport, trCompany, customerNumber, productCategoryNumber, customerName);
            return;
        }
        
        // 3. 默认逻辑（客户名称标准/结论）
        // 只有客户属于集团且有产品指标时才设置
        Company defaultCompany = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
        if (defaultCompany != null && StrUtil.isNotBlank(defaultCompany.getGroupNumber())) {
            if (hasCustomerStandards(defaultCompany.getGroupNumber(), productCategoryNumber)) {
                transportReport.setExecutionStandard(customerName + "标准");
                transportReport.setInspectionConclusion("符合" + customerName + "标准");
            }
        }
    }

    /**
     * 处理公司的执行标准配置
     */
    private void handleExecutionStandardForCompany(TransportQualityInspectionReport transportReport, 
            Company company, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(company.getGeneralExecutionStandard())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(company.getExecutionStandard())) {
            // 检查是否强制应用或客户有指标
            if (Boolean.TRUE.equals(company.getForceApplyExecutionStandard()) || 
                hasCustomerStandards(company.getGroupNumber(), productCategoryNumber)) {
                transportReport.setExecutionStandard(company.getExecutionStandard());
            }
            return;
        }
        
        // 集团判断
        if (StrUtil.isNotBlank(company.getGroupNumber())) {
            Group group = groupMapper.selectOne(
                    new LambdaQueryWrapper<Group>().eq(Group::getGroupNumber, company.getGroupNumber()));
            if (group != null) {
                handleExecutionStandardForGroup(transportReport, group, customerNumber, productCategoryNumber, customerName);
            }
        }
    }

    /**
     * 处理公司的结论配置
     */
    private void handleConclusionForCompany(TransportQualityInspectionReport transportReport, 
            Company company, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(company.getGeneralConclusion())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(company.getConclusion())) {
            // 检查是否强制应用或客户有指标
            if (Boolean.TRUE.equals(company.getForceApplyConclusion()) || 
                hasCustomerStandards(company.getGroupNumber(), productCategoryNumber)) {
                transportReport.setInspectionConclusion(company.getConclusion());
            }
            return;
        }
        
        // 集团判断
        if (StrUtil.isNotBlank(company.getGroupNumber())) {
            Group group = groupMapper.selectOne(
                    new LambdaQueryWrapper<Group>().eq(Group::getGroupNumber, company.getGroupNumber()));
            if (group != null) {
                handleConclusionForGroup(transportReport, group, customerNumber, productCategoryNumber, customerName);
            }
        }
    }

    /**
     * 处理集团的执行标准配置
     */
    private void handleExecutionStandardForGroup(TransportQualityInspectionReport transportReport, 
            Group group, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(group.getGeneralExecutionStandard())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(group.getExecutionStandard())) {
            // 检查是否强制应用或客户有指标
            if (Boolean.TRUE.equals(group.getForceApplyExecutionStandard()) || 
                hasCustomerStandards(group.getGroupNumber(), productCategoryNumber)) {
                transportReport.setExecutionStandard(group.getExecutionStandard());
            }
            return;
        }
        
        // 默认逻辑：只有客户有指标时才设置客户名称标准
        if (hasCustomerStandards(group.getGroupNumber(), productCategoryNumber)) {
            transportReport.setExecutionStandard(customerName + "标准");
        }
    }

    /**
     * 处理集团的结论配置
     */
    private void handleConclusionForGroup(TransportQualityInspectionReport transportReport, 
            Group group, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(group.getGeneralConclusion())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(group.getConclusion())) {
            // 检查是否强制应用或客户有指标
            if (Boolean.TRUE.equals(group.getForceApplyConclusion()) || 
                hasCustomerStandards(group.getGroupNumber(), productCategoryNumber)) {
                transportReport.setInspectionConclusion(group.getConclusion());
            }
            return;
        }
        
        // 默认逻辑：只有客户有指标时才设置客户名称结论
        if (hasCustomerStandards(group.getGroupNumber(), productCategoryNumber)) {
            transportReport.setInspectionConclusion("符合" + customerName + "标准");
        }
    }

    /**
     * 处理运输报告公司的执行标准配置
     */
    private void handleExecutionStandardForTransportReportCompany(TransportQualityInspectionReport transportReport, 
            TransportReportCompany trCompany, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(trCompany.getGeneralExecutionStandard())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(trCompany.getExecutionStandard())) {
            // 检查是否强制应用或客户有指标
            Company relatedCompany = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
            String groupNumber = relatedCompany != null ? relatedCompany.getGroupNumber() : null;
            
            if (Boolean.TRUE.equals(trCompany.getForceApplyExecutionStandard()) || 
                (StrUtil.isNotBlank(groupNumber) && hasCustomerStandards(groupNumber, productCategoryNumber))) {
                transportReport.setExecutionStandard(trCompany.getExecutionStandard());
            }
            return;
        }
        
        // 集团判断
        if (trCompany.getGroupId() != null) {
            TransportReportGroup trGroup = transportReportGroupMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportGroup>().eq(TransportReportGroup::getId,
                            trCompany.getGroupId()));
            if (trGroup != null) {
                handleExecutionStandardForTransportReportGroup(transportReport, trGroup, customerNumber, productCategoryNumber, customerName);
            }
        }
    }

    /**
     * 处理运输报告公司的结论配置
     */
    private void handleConclusionForTransportReportCompany(TransportQualityInspectionReport transportReport, 
            TransportReportCompany trCompany, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(trCompany.getGeneralConclusion())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(trCompany.getConclusion())) {
            // 检查是否强制应用或客户有指标
            Company relatedCompany = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
            String groupNumber = relatedCompany != null ? relatedCompany.getGroupNumber() : null;
            
            if (Boolean.TRUE.equals(trCompany.getForceApplyConclusion()) || 
                (StrUtil.isNotBlank(groupNumber) && hasCustomerStandards(groupNumber, productCategoryNumber))) {
                transportReport.setInspectionConclusion(trCompany.getConclusion());
            }
            return;
        }
        
        // 集团判断
        if (trCompany.getGroupId() != null) {
            TransportReportGroup trGroup = transportReportGroupMapper.selectOne(
                    new LambdaQueryWrapper<TransportReportGroup>().eq(TransportReportGroup::getId,
                            trCompany.getGroupId()));
            if (trGroup != null) {
                handleConclusionForTransportReportGroup(transportReport, trGroup, customerNumber, productCategoryNumber, customerName);
            }
        }
    }

    /**
     * 处理运输报告集团的执行标准配置
     */
    private void handleExecutionStandardForTransportReportGroup(TransportQualityInspectionReport transportReport, 
            TransportReportGroup trGroup, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(trGroup.getGeneralExecutionStandard())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(trGroup.getExecutionStandard())) {
            // 检查是否强制应用或客户有指标
            Company relatedCompany = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
            String groupNumber = relatedCompany != null ? relatedCompany.getGroupNumber() : null;
            
            if (Boolean.TRUE.equals(trGroup.getForceApplyExecutionStandard()) || 
                (StrUtil.isNotBlank(groupNumber) && hasCustomerStandards(groupNumber, productCategoryNumber))) {
                transportReport.setExecutionStandard(trGroup.getExecutionStandard());
            }
        }
    }

    /**
     * 处理运输报告集团的结论配置
     */
    private void handleConclusionForTransportReportGroup(TransportQualityInspectionReport transportReport, 
            TransportReportGroup trGroup, String customerNumber, String productCategoryNumber, String customerName) {
        if (Boolean.TRUE.equals(trGroup.getGeneralConclusion())) {
            // 强制通用，什么都不做
            return;
        }
        
        if (StrUtil.isNotBlank(trGroup.getConclusion())) {
            // 检查是否强制应用或客户有指标
            Company relatedCompany = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>().eq(Company::getCompanyNumber, customerNumber));
            String groupNumber = relatedCompany != null ? relatedCompany.getGroupNumber() : null;
            
            if (Boolean.TRUE.equals(trGroup.getForceApplyConclusion()) || 
                (StrUtil.isNotBlank(groupNumber) && hasCustomerStandards(groupNumber, productCategoryNumber))) {
                transportReport.setInspectionConclusion(trGroup.getConclusion());
            }
        }
    }

    /**
     * 判断客户是否对指定产品类别有质量标准（指标）
     * 
     * @param groupNumber 集团编号
     * @param productCategoryNumber 产品类别编号
     * @return true: 有指标，false: 无指标
     */
    private boolean hasCustomerStandards(String groupNumber, String productCategoryNumber) {
        if (StrUtil.isBlank(groupNumber) || StrUtil.isBlank(productCategoryNumber)) {
            return false;
        }
        
        List<GroupQualityStandard> gqsList = groupQualityStandardMapper.selectList(
                new LambdaQueryWrapper<GroupQualityStandard>()
                        .eq(GroupQualityStandard::getGroupNumber, groupNumber)
                        .eq(GroupQualityStandard::getProductCategoryNumber, productCategoryNumber));
        
        return CollUtil.isNotEmpty(gqsList);
    }

    /**
     * 为Excel文件移除位置靠下的图片（权限处理）
     * 如果只有一个图片则直接去掉，如果有多个图片则去掉位置最靠下的图片
     * 
     * @param sheet 工作表
     */
    private void removeBottomImagesForExcel(Worksheet sheet) {
        try {
            int pictureCount = sheet.getPictures().getCount();
            
            if (pictureCount == 0) {
                log.info("工作表中没有图片，无需处理");
                return;
            }
            
            if (pictureCount == 1) {
                // 如果只有一个图片，直接移除
                sheet.getPictures().get(0).remove();
                log.info("移除了工作表中的唯一图片");
                return;
            }
            
            // 如果有多个图片，找到位置最靠下的图片并移除
            int bottomMostIndex = 0;
            int maxTopRow = -1;
            
            for (int i = 0; i < pictureCount; i++) {
                try {
                    ExcelPicture picture = sheet.getPictures().get(i);
                    int topRow = picture.getTopRow();
                    
                    if (topRow > maxTopRow) {
                        maxTopRow = topRow;
                        bottomMostIndex = i;
                    }
                } catch (Exception e) {
                    log.warn("获取第{}个图片位置信息时发生异常: {}", i, e.getMessage());
                }
            }
            
            // 移除位置最靠下的图片
            sheet.getPictures().get(bottomMostIndex).remove();
            log.info("移除了位置最靠下的图片，原位置行号: {}", maxTopRow);
            
        } catch (Exception e) {
            log.error("移除图片时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 为PDF文件添加权限保护，防止修改（增强安全版本）
     * 
     * @param pdfFilePath PDF文件路径
     */
    private void addPdfProtection(String pdfFilePath) {
        try {
            long protectionStartTime = System.currentTimeMillis();
            log.info("开始为PDF添加高安全性权限保护: {}", pdfFilePath);
            
            // 创建PdfDocument实例
            PdfDocument pdfDoc = new PdfDocument();
            
            // 加载已生成的PDF文件
            pdfDoc.loadFromFile(pdfFilePath);
            
            // 设置权限保护参数
            String openPassword = ""; // 打开密码：无（用户可以正常打开和查看）
            String permissionPassword = generateSecurePassword(); // 权限密码：高强度随机生成
            PdfEncryptionKeySize keySize = PdfEncryptionKeySize.Key_256_Bit; // 使用256位加密（最高安全级别）
            
            // 设置允许的权限：仅允许打印，禁止修改、复制等操作
            EnumSet<PdfPermissionsFlags> flags = EnumSet.of(PdfPermissionsFlags.Print);
            
            // 加密文档
            pdfDoc.getSecurity().encrypt(openPassword, permissionPassword, flags, keySize);
            
            // 保存受保护的PDF文件（覆盖原文件）
            pdfDoc.saveToFile(pdfFilePath);
            pdfDoc.close();
            
            double protectionDuration = (System.currentTimeMillis() - protectionStartTime) / 1000.0;
            log.info("PDF高安全性权限保护添加完成，耗时: {}秒，加密级别: 256位AES", String.format("%.2f", protectionDuration));
            
        } catch (Exception e) {
            log.error("为PDF添加权限保护时发生异常: {}", e.getMessage(), e);
            // 异常时不影响PDF的正常使用，只是没有保护而已
        }
    }
    
    /**
     * 生成高强度随机密码用于PDF权限保护
     * 
     * @return 32位高强度随机密码
     */
    private String generateSecurePassword() {
        try {
            // 使用密码学安全的随机数生成器
            SecureRandom secureRandom = new SecureRandom();
            
            // 生成32字节的随机数据
            byte[] randomBytes = new byte[32];
            secureRandom.nextBytes(randomBytes);
            
            // 转换为Base64编码，确保包含各种字符类型
            String base64Password = Base64.getEncoder().encodeToString(randomBytes);
            
            // 添加时间戳和固定前缀，进一步增强唯一性
            String timestamp = String.valueOf(System.currentTimeMillis());
            String complexPassword = "HH_SEC_" + timestamp + "_" + base64Password;
            
            // 截取前64位，确保密码长度适中但足够复杂
            return complexPassword.length() > 64 ? complexPassword.substring(0, 64) : complexPassword;
            
        } catch (Exception e) {
            log.warn("生成高强度密码失败，使用备用方案: {}", e.getMessage());
            // 备用方案：如果SecureRandom失败，使用增强的时间戳方案
            return "HH_PDF_BACKUP_" + System.currentTimeMillis() + "_" + System.nanoTime();
        }
    }

    @Override
    public String exportTransportReportData(String groupNumber, List<String> customerNumbers,
            List<String> productCategoryNumbers, String startDate, String endDate,
            Boolean separateByCustomer) {

        log.info("开始导出随车质检单数据 - 集团: {}, 客户: {}, 产品类别: {}, 时间范围: {} ~ {}, 按客户分表: {}",
                groupNumber, customerNumbers, productCategoryNumbers, startDate, endDate, separateByCustomer);

        try {
            // 1. 参数验证 - 修改验证逻辑：如果未选择集团，客户选择才是必填项
            if (StrUtil.isBlank(groupNumber) && CollUtil.isEmpty(customerNumbers)) {
                throw new IllegalArgumentException("未选择集团时，客户选择为必填项");
            }

            // 2. 构建查询条件
            LambdaQueryWrapper<OutboundInformationTransferRecord> queryWrapper = buildExportQueryWrapper(
                    groupNumber, customerNumbers, productCategoryNumbers, startDate, endDate);

            // 3. 查询出库传递单数据
            List<OutboundInformationTransferRecord> transferRecords = transferRecordMapper.selectList(queryWrapper);

            if (CollUtil.isEmpty(transferRecords)) {
                log.warn("未查询到符合条件的数据");
                throw new RuntimeException("未查询到符合条件的数据");
            }

            log.info("查询到 {} 条出库传递单记录", transferRecords.size());

            // 4. 获取随车质检单数据
            List<TransportReportExportData> exportDataList = buildExportDataList(transferRecords);

            if (CollUtil.isEmpty(exportDataList)) {
                log.warn("未查询到对应的随车质检单数据");
                throw new RuntimeException("未查询到对应的随车质检单数据");
            }

            log.info("获取到 {} 条随车质检单数据", exportDataList.size());

            // 5. 按规则分组数据
            Map<String, List<TransportReportExportData>> groupedData = groupExportData(exportDataList, separateByCustomer);

            // 6. 生成Excel文件
            String fileName = generateExportFileName(groupNumber, customerNumbers, startDate, endDate);
            String filePath = createExcelFile(fileName, groupedData);

            log.info("随车质检单数据导出完成，文件路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("导出随车质检单数据失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建导出查询条件
     * 注意：只导出已完成两次审核（初审和复审都通过）的出库传递单记录
     */
    private LambdaQueryWrapper<OutboundInformationTransferRecord> buildExportQueryWrapper(
            String groupNumber, List<String> customerNumbers, List<String> productCategoryNumbers,
            String startDate, String endDate) {

        LambdaQueryWrapper<OutboundInformationTransferRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已完成两次审核的记录（初审和复审都通过）
        queryWrapper.eq(OutboundInformationTransferRecord::getFirstReviewStatus, true)
                   .eq(OutboundInformationTransferRecord::getSecondReviewStatus, true);

        // 集团条件
        if (StrUtil.isNotBlank(groupNumber)) {
            // 如果指定了集团，需要查询该集团下的所有客户
            List<Company> companies = companyMapper.selectList(
                    new LambdaQueryWrapper<Company>().eq(Company::getGroupNumber, groupNumber));

            if (CollUtil.isNotEmpty(companies)) {
                List<String> groupCustomerNumbers = companies.stream()
                        .map(Company::getCompanyNumber)
                        .collect(Collectors.toList());

                // 如果同时指定了客户，取交集
                if (CollUtil.isNotEmpty(customerNumbers)) {
                    groupCustomerNumbers.retainAll(customerNumbers);
                }

                if (CollUtil.isNotEmpty(groupCustomerNumbers)) {
                    queryWrapper.in(OutboundInformationTransferRecord::getCustomerNumber, groupCustomerNumbers);
                }
            }
        } else if (CollUtil.isNotEmpty(customerNumbers)) {
            // 只指定了客户
            queryWrapper.in(OutboundInformationTransferRecord::getCustomerNumber, customerNumbers);
        }

        // 产品类别条件
        if (CollUtil.isNotEmpty(productCategoryNumbers)) {
            queryWrapper.in(OutboundInformationTransferRecord::getProductCategoryNumber, productCategoryNumbers);
        }

        // 时间范围条件
        if (StrUtil.isNotBlank(startDate)) {
            queryWrapper.ge(OutboundInformationTransferRecord::getOutboundDate, startDate);
        }
        if (StrUtil.isNotBlank(endDate)) {
            queryWrapper.le(OutboundInformationTransferRecord::getOutboundDate, endDate);
        }

        // 如果没有指定时间范围，默认查询当前月份
        if (StrUtil.isBlank(startDate) && StrUtil.isBlank(endDate)) {
            String currentMonth = DateUtil.format(new Date(), "yyyy-MM");
            queryWrapper.like(OutboundInformationTransferRecord::getOutboundDate, currentMonth);
        }

        // 按出库日期排序
        queryWrapper.orderByDesc(OutboundInformationTransferRecord::getOutboundDate);

        return queryWrapper;
    }

    /**
     * 构建导出数据列表
     */
    private List<TransportReportExportData> buildExportDataList(List<OutboundInformationTransferRecord> transferRecords) {
        List<TransportReportExportData> exportDataList = new ArrayList<>();

        // 按出库单号和产品名称分组
        Map<String, List<OutboundInformationTransferRecord>> groupedRecords = transferRecords.stream()
                .collect(Collectors.groupingBy(record ->
                        record.getOutboundNumber() + "_" + record.getProductName()));

        for (Map.Entry<String, List<OutboundInformationTransferRecord>> entry : groupedRecords.entrySet()) {
            List<OutboundInformationTransferRecord> records = entry.getValue();
            OutboundInformationTransferRecord firstRecord = records.get(0);

            // 查询对应的随车质检单
            TransportQualityInspectionReport transportReport = transportQualityInspectionReportMapper.selectOne(
                    new LambdaQueryWrapper<TransportQualityInspectionReport>()
                            .eq(TransportQualityInspectionReport::getLinkId, firstRecord.getLinkId())
                            .eq(TransportQualityInspectionReport::getOutboundNumber, firstRecord.getOutboundNumber())
                            .eq(TransportQualityInspectionReport::getProductName, firstRecord.getProductName()));

            if (transportReport == null) {
                log.warn("未找到对应的随车质检单: 出库单号={}, 产品名称={}",
                        firstRecord.getOutboundNumber(), firstRecord.getProductName());
                continue;
            }

            // 查询随车质检单数据
            List<TransportQualityInspectionData> transportDataList = transportQualityInspectionDataMapper.selectList(
                    new LambdaQueryWrapper<TransportQualityInspectionData>()
                            .eq(TransportQualityInspectionData::getReportId, transportReport.getId()));

            // 为每个生产批号创建一条导出数据
            for (TransportQualityInspectionData transportData : transportDataList) {
                TransportReportExportData exportData = new TransportReportExportData();

                // 基本信息
                exportData.setCustomerName(transportReport.getCustomerName());
                exportData.setProductName(transportReport.getProductName());
                exportData.setProductCategoryName(getProductCategoryName(firstRecord.getProductCategoryNumber()));
                exportData.setProductCategoryNumber(firstRecord.getProductCategoryNumber());
                exportData.setQuantity(transportReport.getWeightInTons());
                exportData.setSpecification(transportReport.getPackagingSpecifications());
                exportData.setExecutionStandard(transportReport.getExecutionStandard());
                exportData.setProductionBatch(transportData.getProductionBatch());
                exportData.setProductionDate(transportData.getProductionDate());
                exportData.setInspectionConclusion(transportReport.getInspectionConclusion());
                exportData.setInspector(transportReport.getInspector());
                exportData.setVerifier(transportReport.getVerifier());
                exportData.setAuditor(transportReport.getAuditor());
                exportData.setReportDate(transportReport.getReportDate());

                // 解析检测数据
                Map<String, String> inspectionDataMap = parseInspectionData(transportData.getInspectionData());
                exportData.setInspectionData(inspectionDataMap);

                exportDataList.add(exportData);
            }
        }

        return exportDataList;
    }

    /**
     * 获取产品类别名称
     */
    private String getProductCategoryName(String productCategoryNumber) {
        if (StrUtil.isBlank(productCategoryNumber)) {
            return "";
        }

        // 特殊处理预分散体产品
        if (productCategoryNumber.startsWith("0105")) {
            return "预分散体产品";
        }

        // 查询产品类别表获取名称
        try {
            MaterialCategory category = materialCategoryMapper.selectOne(
                    new LambdaQueryWrapper<MaterialCategory>()
                            .eq(MaterialCategory::getCategoryNumber, productCategoryNumber));

            if (category != null && StrUtil.isNotBlank(category.getCategoryName())) {
                return category.getCategoryName();
            }
        } catch (Exception e) {
            log.warn("查询产品类别名称失败: {}", productCategoryNumber, e);
        }

        // 如果查询失败，返回编号
        return productCategoryNumber;
    }

    /**
     * 解析检测数据字符串
     */
    private Map<String, String> parseInspectionData(String inspectionDataStr) {
        Map<String, String> dataMap = new LinkedHashMap<>();

        if (StrUtil.isBlank(inspectionDataStr)) {
            return dataMap;
        }

        // 按分号分割检测数据
        String[] dataItems = inspectionDataStr.split(";");
        for (String item : dataItems) {
            if (StrUtil.isNotBlank(item) && item.contains(":")) {
                String[] parts = item.split(":", 2);
                if (parts.length == 2) {
                    dataMap.put(parts[0].trim(), parts[1].trim());
                }
            }
        }

        return dataMap;
    }

    /**
     * 按规则分组导出数据
     */
    private Map<String, List<TransportReportExportData>> groupExportData(
            List<TransportReportExportData> exportDataList, Boolean separateByCustomer) {

        Map<String, List<TransportReportExportData>> groupedData = new LinkedHashMap<>();

        if (Boolean.TRUE.equals(separateByCustomer)) {
            // 按客户和产品类别分组
            Map<String, List<TransportReportExportData>> tempGrouped = exportDataList.stream()
                    .collect(Collectors.groupingBy(data -> {
                        String customerPrefix = data.getCustomerName().length() > 6 ?
                                data.getCustomerName().substring(0, 6) : data.getCustomerName();
                        return customerPrefix + "_" + data.getProductCategoryName();
                    }, LinkedHashMap::new, Collectors.toList()));

            // 处理预分散体特殊规则
            groupedData.putAll(handlePreDispersionProducts(tempGrouped, true));
        } else {
            // 只按产品类别分组
            Map<String, List<TransportReportExportData>> tempGrouped = exportDataList.stream()
                    .collect(Collectors.groupingBy(TransportReportExportData::getProductCategoryName,
                            LinkedHashMap::new, Collectors.toList()));

            // 处理预分散体特殊规则
            groupedData.putAll(handlePreDispersionProducts(tempGrouped, false));
        }

        return groupedData;
    }

    /**
     * 处理预分散体产品的特殊分类规则
     */
    private Map<String, List<TransportReportExportData>> handlePreDispersionProducts(
            Map<String, List<TransportReportExportData>> originalGrouped, boolean separateByCustomer) {

        Map<String, List<TransportReportExportData>> result = new LinkedHashMap<>();
        List<TransportReportExportData> preDispersionProducts = new ArrayList<>();

        for (Map.Entry<String, List<TransportReportExportData>> entry : originalGrouped.entrySet()) {
            String groupKey = entry.getKey();
            List<TransportReportExportData> dataList = entry.getValue();

            // 检查是否包含预分散体产品（产品类别编号以"0105"开头）
            List<TransportReportExportData> preDispersionInGroup = dataList.stream()
                    .filter(data -> StrUtil.isNotBlank(data.getProductCategoryNumber()) &&
                            data.getProductCategoryNumber().startsWith("0105"))
                    .collect(Collectors.toList());

            List<TransportReportExportData> otherProducts = dataList.stream()
                    .filter(data -> StrUtil.isBlank(data.getProductCategoryNumber()) ||
                            !data.getProductCategoryNumber().startsWith("0105"))
                    .collect(Collectors.toList());

            // 预分散体产品单独收集
            preDispersionProducts.addAll(preDispersionInGroup);

            // 其他产品保持原分组
            if (CollUtil.isNotEmpty(otherProducts)) {
                result.put(groupKey, otherProducts);
            }
        }

        // 所有预分散体产品归类到同一个工作表
        if (CollUtil.isNotEmpty(preDispersionProducts)) {
            String preDispersionSheetName = separateByCustomer ?
                    "预分散体" : "预分散体产品";
            result.put(preDispersionSheetName, preDispersionProducts);
        }

        return result;
    }

    /**
     * 生成导出文件名
     */
    private String generateExportFileName(String groupNumber, List<String> customerNumbers,
            String startDate, String endDate) {

        StringBuilder fileName = new StringBuilder("随车质检单数据导出");

        // 添加集团名称（如果选择了集团）
        if (StrUtil.isNotBlank(groupNumber)) {
            try {
                Group group = groupMapper.selectOne(
                        new LambdaQueryWrapper<Group>().eq(Group::getGroupNumber, groupNumber));
                if (group != null && StrUtil.isNotBlank(group.getGroupName())) {
                    fileName.append("_").append(group.getGroupName());
                } else {
                    fileName.append("_集团").append(groupNumber);
                }
            } catch (Exception e) {
                log.warn("查询集团名称失败: {}", groupNumber, e);
                fileName.append("_集团").append(groupNumber);
            }
        }

        // 添加客户数量信息
        if (CollUtil.isNotEmpty(customerNumbers)) {
            fileName.append("_").append(customerNumbers.size()).append("个客户");
        } else if (StrUtil.isNotBlank(groupNumber)) {
            // 如果选择了集团但没有选择具体客户，表示导出该集团下所有客户
            fileName.append("_全部客户");
        }

        // 添加时间范围
        if (StrUtil.isNotBlank(startDate) && StrUtil.isNotBlank(endDate)) {
            fileName.append("_").append(startDate).append("至").append(endDate);
        } else if (StrUtil.isBlank(startDate) && StrUtil.isBlank(endDate)) {
            // 默认当前月份
            fileName.append("_").append(DateUtil.format(new Date(), "yyyy-MM"));
        }

        fileName.append(".xlsx");

        return fileName.toString();
    }

    /**
     * 创建Excel文件
     */
    private String createExcelFile(String fileName, Map<String, List<TransportReportExportData>> groupedData) {
        String fullPath = filePath + "export/" + fileName;

        // 确保目录存在
        File directory = new File(filePath + "export/");
        if (!directory.exists()) {
            directory.mkdirs();
        }

        try (ExcelWriter excelWriter = FastExcel.write(fullPath)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build()) {

            int sheetIndex = 0;

            for (Map.Entry<String, List<TransportReportExportData>> entry : groupedData.entrySet()) {
                String sheetName = entry.getKey();
                List<TransportReportExportData> dataList = entry.getValue();

                if (CollUtil.isEmpty(dataList)) {
                    continue;
                }

                log.info("创建工作表: {}, 包含 {} 条记录", sheetName, dataList.size());

                // 构建表头和数据
                List<List<String>> headList = buildExcelHeaders(dataList);
                List<List<String>> dataRows = buildExcelData(dataList);

                // 验证表头和数据的一致性
                validateExcelDataConsistency(headList, dataRows, sheetName);

                // 创建工作表
                WriteSheet writeSheet = FastExcel.writerSheet(sheetIndex, sheetName)
                        .head(headList)
                        .build();

                // 写入数据
                excelWriter.write(dataRows, writeSheet);
                sheetIndex++;
            }

            log.info("Excel文件创建完成: {}", fullPath);
            return fullPath;

        } catch (Exception e) {
            log.error("创建Excel文件失败: {}", fileName, e);
            throw new RuntimeException("创建Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建Excel表头
     */
    private List<List<String>> buildExcelHeaders(List<TransportReportExportData> dataList) {
        List<List<String>> headList = new ArrayList<>();

        // 固定列
        headList.add(Collections.singletonList("客户名称"));
        headList.add(Collections.singletonList("产品名称"));
        headList.add(Collections.singletonList("数量"));
        headList.add(Collections.singletonList("规格"));
        headList.add(Collections.singletonList("生产批号"));
        headList.add(Collections.singletonList("生产日期"));

        // 动态检测项目列 - 全局收集所有检测项目名称
        Set<String> allInspectionItems = collectAllInspectionItems(dataList);
        for (String itemName : allInspectionItems) {
            headList.add(Collections.singletonList(itemName));
        }

        // 固定列（结尾）
        headList.add(Collections.singletonList("出单日期"));

        return headList;
    }

    /**
     * 收集所有检测项目名称（保持一致的顺序）
     */
    private Set<String> collectAllInspectionItems(List<TransportReportExportData> dataList) {
        Set<String> allInspectionItems = new LinkedHashSet<>();

        if (CollUtil.isNotEmpty(dataList)) {
            for (TransportReportExportData data : dataList) {
                if (data.getInspectionData() != null) {
                    allInspectionItems.addAll(data.getInspectionData().keySet());
                }
            }
        }

        log.debug("收集到 {} 个检测项目: {}", allInspectionItems.size(), allInspectionItems);
        return allInspectionItems;
    }

    /**
     * 构建Excel数据行
     */
    private List<List<String>> buildExcelData(List<TransportReportExportData> dataList) {
        List<List<String>> dataRows = new ArrayList<>();

        // 优化排序逻辑：主要按出单日期升序，次要按生产日期升序
        List<TransportReportExportData> sortedDataList = dataList.stream()
                .sorted((data1, data2) -> {
                    // 主要排序：出单日期
                    String reportDate1 = StrUtil.nullToEmpty(data1.getReportDate());
                    String reportDate2 = StrUtil.nullToEmpty(data2.getReportDate());

                    // 处理日期为空的情况：空值排在最后
                    if (StrUtil.isBlank(reportDate1) && StrUtil.isBlank(reportDate2)) {
                        // 两个都为空，按生产日期比较
                        String productionDate1 = StrUtil.nullToEmpty(data1.getProductionDate());
                        String productionDate2 = StrUtil.nullToEmpty(data2.getProductionDate());
                        return productionDate1.compareTo(productionDate2);
                    } else if (StrUtil.isBlank(reportDate1)) {
                        return 1; // data1排在后面
                    } else if (StrUtil.isBlank(reportDate2)) {
                        return -1; // data2排在后面
                    }

                    // 主要排序比较
                    int reportDateComparison = reportDate1.compareTo(reportDate2);
                    if (reportDateComparison != 0) {
                        return reportDateComparison;
                    }

                    // 次要排序：生产日期
                    String productionDate1 = StrUtil.nullToEmpty(data1.getProductionDate());
                    String productionDate2 = StrUtil.nullToEmpty(data2.getProductionDate());
                    return productionDate1.compareTo(productionDate2);
                })
                .collect(Collectors.toList());

        // 使用统一的检测项目收集方法
        Set<String> allInspectionItems = collectAllInspectionItems(dataList);

        // 验证列数一致性
        int expectedColumnCount = 6 + allInspectionItems.size() + 1; // 固定列(6) + 动态列 + 出单日期(1)
        log.debug("预期列数: {}, 检测项目数: {}", expectedColumnCount, allInspectionItems.size());

        for (TransportReportExportData data : sortedDataList) {
            List<String> row = new ArrayList<>();

            // 固定列数据
            row.add(StrUtil.nullToEmpty(data.getCustomerName()));
            row.add(StrUtil.nullToEmpty(data.getProductName()));
            row.add(StrUtil.nullToEmpty(data.getQuantity()));
            row.add(StrUtil.nullToEmpty(data.getSpecification()));
            row.add(StrUtil.nullToEmpty(data.getProductionBatch()));
            row.add(StrUtil.nullToEmpty(data.getProductionDate()));

            // 检测项目数据 - 确保与表头列数一致
            Map<String, String> inspectionData = data.getInspectionData();
            for (String itemName : allInspectionItems) {
                String value = inspectionData != null ? inspectionData.get(itemName) : "";
                row.add(StrUtil.nullToEmpty(value));
            }

            // 固定列数据（结尾）
            row.add(StrUtil.nullToEmpty(data.getReportDate()));

            // 验证行列数
            if (row.size() != expectedColumnCount) {
                log.warn("数据行列数不匹配 - 预期: {}, 实际: {}, 记录: {}",
                    expectedColumnCount, row.size(), data.getCustomerName() + "-" + data.getProductName());
            }

            dataRows.add(row);
        }

        log.info("构建Excel数据完成，共 {} 行数据，每行 {} 列", dataRows.size(),
            dataRows.isEmpty() ? 0 : dataRows.get(0).size());

        return dataRows;
    }

    @Override
    public void exportTransportReportDataToResponse(String groupNumber, List<String> customerNumbers,
            List<String> productCategoryNumbers, String startDate, String endDate,
            Boolean separateByCustomer, HttpServletResponse response) {

        log.info("开始导出随车质检单数据到响应流 - 集团: {}, 客户数量: {}, 产品类别数量: {}, 时间范围: {} ~ {}, 按客户分文件: {}",
                groupNumber,
                customerNumbers != null ? customerNumbers.size() : 0,
                productCategoryNumbers != null ? productCategoryNumbers.size() : 0,
                startDate, endDate, separateByCustomer);

        try {
            // 1. 参数验证 - 修改验证逻辑：如果未选择集团，客户选择才是必填项
            if (StrUtil.isBlank(groupNumber) && CollUtil.isEmpty(customerNumbers)) {
                throw new IllegalArgumentException("未选择集团时，客户选择为必填项");
            }

            // 2. 构建查询条件
            LambdaQueryWrapper<OutboundInformationTransferRecord> queryWrapper = buildExportQueryWrapper(
                    groupNumber, customerNumbers, productCategoryNumbers, startDate, endDate);

            // 3. 查询出库传递单数据
            List<OutboundInformationTransferRecord> transferRecords = transferRecordMapper.selectList(queryWrapper);

            if (CollUtil.isEmpty(transferRecords)) {
                log.warn("未查询到符合条件的数据");
                throw new RuntimeException("未查询到符合条件的数据");
            }

            log.info("查询到 {} 条已完成两次审核的出库传递单记录", transferRecords.size());

            // 4. 获取随车质检单数据
            List<TransportReportExportData> exportDataList = buildExportDataList(transferRecords);

            if (CollUtil.isEmpty(exportDataList)) {
                log.warn("未查询到对应的随车质检单数据");
                throw new RuntimeException("未查询到对应的随车质检单数据");
            }

            log.info("获取到 {} 条随车质检单数据", exportDataList.size());

            // 5. 判断是否按客户分文件
            if (Boolean.TRUE.equals(separateByCustomer)) {
                // 按客户分文件导出，生成ZIP压缩包
                exportSeparateFilesByCustomer(exportDataList, groupNumber, customerNumbers, startDate, endDate, response);
            } else {
                // 传统的单文件导出
                Map<String, List<TransportReportExportData>> groupedData = groupExportData(exportDataList, false);
                String fileName = generateExportFileName(groupNumber, customerNumbers, startDate, endDate);

                // 设置响应头
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" +
                        java.net.URLEncoder.encode(fileName, "UTF-8"));

                // 直接写入响应流
                createExcelFileToStream(groupedData, response.getOutputStream());
            }

            log.info("随车质检单数据导出完成");

        } catch (Exception e) {
            log.error("导出随车质检单数据到响应流失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按客户分文件导出，生成ZIP压缩包
     */
    private void exportSeparateFilesByCustomer(List<TransportReportExportData> exportDataList,
            String groupNumber, List<String> customerNumbers, String startDate, String endDate,
            HttpServletResponse response) throws Exception {

        log.info("开始按客户分文件导出");

        // 1. 按客户名称分组数据
        Map<String, List<TransportReportExportData>> customerGroupedData = exportDataList.stream()
                .collect(Collectors.groupingBy(
                    TransportReportExportData::getCustomerName,
                    LinkedHashMap::new,
                    Collectors.toList()
                ));

        log.info("按客户分组完成，共 {} 个客户", customerGroupedData.size());

        // 2. 为每个客户生成Excel文件的字节数组
        Map<String, byte[]> customerExcelFiles = new HashMap<>();

        for (Map.Entry<String, List<TransportReportExportData>> entry : customerGroupedData.entrySet()) {
            String customerName = entry.getKey();
            List<TransportReportExportData> customerData = entry.getValue();

            // 对每个客户的数据按产品类别分组（保持原有逻辑）
            Map<String, List<TransportReportExportData>> productGroupedData = groupExportData(customerData, false);

            // 生成Excel文件字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            createExcelFileToStream(productGroupedData, baos);

            // 使用客户名称作为文件名
            String fileName = sanitizeFileName(customerName) + "_随车质检单数据.xlsx";
            customerExcelFiles.put(fileName, baos.toByteArray());

            log.info("为客户 {} 生成Excel文件，包含 {} 条记录", customerName, customerData.size());
        }

        // 3. 生成ZIP文件名
        String zipFileName = generateExportFileName(groupNumber, customerNumbers, startDate, endDate);
        if (!zipFileName.endsWith(".zip")) {
            zipFileName = zipFileName.replace(".xlsx", ".zip");
        }

        // 4. 设置响应头为ZIP文件
        response.setContentType("application/zip");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" +
                java.net.URLEncoder.encode(zipFileName, "UTF-8"));

        // 5. 创建ZIP文件并写入响应流
        createZipFileToStream(customerExcelFiles, response.getOutputStream());

        log.info("按客户分文件导出完成，ZIP文件包含 {} 个Excel文件", customerExcelFiles.size());
    }

    /**
     * 创建ZIP文件并写入输出流
     */
    private void createZipFileToStream(Map<String, byte[]> files, OutputStream outputStream) throws Exception {
        try (ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(outputStream)) {
            zipOut.setUseZip64(Zip64Mode.AsNeeded);

            for (Map.Entry<String, byte[]> entry : files.entrySet()) {
                String fileName = entry.getKey();
                byte[] fileData = entry.getValue();

                ZipArchiveEntry zipEntry = new ZipArchiveEntry(fileName);
                zipEntry.setSize(fileData.length);
                zipOut.putArchiveEntry(zipEntry);
                zipOut.write(fileData);
                zipOut.closeArchiveEntry();
            }

            zipOut.finish();
        }
    }

    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "未知客户";
        }
        // 移除或替换文件名中的非法字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    /**
     * 创建Excel文件并写入输出流
     */
    private void createExcelFileToStream(Map<String, List<TransportReportExportData>> groupedData, OutputStream outputStream) {
        try (ExcelWriter excelWriter = FastExcel.write(outputStream)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build()) {

            int sheetIndex = 0;

            for (Map.Entry<String, List<TransportReportExportData>> entry : groupedData.entrySet()) {
                String sheetName = entry.getKey();
                List<TransportReportExportData> dataList = entry.getValue();

                if (CollUtil.isEmpty(dataList)) {
                    continue;
                }

                log.info("创建工作表: {}, 包含 {} 条记录", sheetName, dataList.size());

                // 构建表头和数据
                List<List<String>> headList = buildExcelHeaders(dataList);
                List<List<String>> dataRows = buildExcelData(dataList);

                // 验证表头和数据的一致性
                validateExcelDataConsistency(headList, dataRows, sheetName);

                // 创建工作表
                WriteSheet writeSheet = FastExcel.writerSheet(sheetIndex, sheetName)
                        .head(headList)
                        .build();

                // 写入数据
                excelWriter.write(dataRows, writeSheet);
                sheetIndex++;
            }

            log.info("Excel文件写入输出流完成");

        } catch (Exception e) {
            log.error("创建Excel文件到输出流失败", e);
            throw new RuntimeException("创建Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, String>> getGroupOptions() {
        log.info("获取集团选项列表");

        try {
            List<Group> groups = groupMapper.selectList(
                    new LambdaQueryWrapper<Group>()
                            .isNotNull(Group::getGroupNumber)
                            .isNotNull(Group::getGroupName)
                            .orderBy(true, true, Group::getGroupNumber));

            return groups.stream()
                    .map(group -> {
                        Map<String, String> option = new HashMap<>();
                        option.put("value", group.getGroupNumber());
                        option.put("label", group.getGroupName());
                        return option;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取集团选项失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, String>> getCustomerOptions(String groupNumber, String keyword) {
        log.info("获取客户选项列表 - 集团: {}, 关键字: {}", groupNumber, keyword);

        try {
            List<Map<String, String>> options = new ArrayList<>();

            if (StrUtil.isNotBlank(groupNumber)) {
                // 查询指定集团下的客户
                List<Company> companies = companyMapper.selectList(
                        new LambdaQueryWrapper<Company>()
                                .eq(Company::getGroupNumber, groupNumber)
                                .like(StrUtil.isNotBlank(keyword), Company::getCompanyName, keyword)
                                .isNotNull(Company::getCompanyNumber)
                                .isNotNull(Company::getCompanyName)
                                .orderBy(true, true, Company::getCompanyNumber));

                options = companies.stream()
                        .map(company -> {
                            Map<String, String> option = new HashMap<>();
                            option.put("value", company.getCompanyNumber());
                            option.put("label", company.getCompanyName());
                            return option;
                        })
                        .collect(Collectors.toList());
            } else {
                // 查询通用客户
                List<CommonCompany> commonCompanies = commonCompanyMapper.selectList(
                        new LambdaQueryWrapper<CommonCompany>()
                                .like(StrUtil.isNotBlank(keyword), CommonCompany::getCompanyName, keyword)
                                .isNotNull(CommonCompany::getCompanyNumber)
                                .isNotNull(CommonCompany::getCompanyName)
                                .orderBy(true, true, CommonCompany::getCompanyNumber));

                options = commonCompanies.stream()
                        .map(company -> {
                            Map<String, String> option = new HashMap<>();
                            option.put("value", company.getCompanyNumber());
                            option.put("label", company.getCompanyName());
                            return option;
                        })
                        .collect(Collectors.toList());
            }

            log.info("获取到 {} 个客户选项", options.size());
            return options;

        } catch (Exception e) {
            log.error("获取客户选项失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, String>> getProductCategoryOptions() {
        log.info("获取产品类别选项列表");

        try {
            // 1. 从MaterialInformation中获取所有不重复的categoryNumber
            List<String> categoryNumbers = materialInformationMapper.selectList(
                    new LambdaQueryWrapper<MaterialInformation>()
                            .select(MaterialInformation::getCategoryNumber)
                            .isNotNull(MaterialInformation::getCategoryNumber)
                            .groupBy(MaterialInformation::getCategoryNumber))
                    .stream()
                    .map(MaterialInformation::getCategoryNumber)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(categoryNumbers)) {
                return new ArrayList<>();
            }

            // 2. 查询对应的类别名称
            List<MaterialCategory> categories = materialCategoryMapper.selectList(
                    new LambdaQueryWrapper<MaterialCategory>()
                            .in(MaterialCategory::getCategoryNumber, categoryNumbers)
                            .orderBy(true, true, MaterialCategory::getCategoryNumber));

            // 3. 构建选项列表
            List<Map<String, String>> options = new ArrayList<>();

            // 4. 处理预分散体特殊规则
            List<MaterialCategory> preDispersionCategories = categories.stream()
                    .filter(category -> category.getCategoryNumber().startsWith("0105"))
                    .collect(Collectors.toList());

            List<MaterialCategory> otherCategories = categories.stream()
                    .filter(category -> !category.getCategoryNumber().startsWith("0105"))
                    .collect(Collectors.toList());

            // 5. 添加预分散体合并选项
            if (CollUtil.isNotEmpty(preDispersionCategories)) {
                Map<String, String> preDispersionOption = new HashMap<>();
                preDispersionOption.put("value", "0105");
                preDispersionOption.put("label", "预分散体产品");
                options.add(preDispersionOption);
            }

            // 6. 添加其他类别选项
            for (MaterialCategory category : otherCategories) {
                Map<String, String> option = new HashMap<>();
                option.put("value", category.getCategoryNumber());
                option.put("label", category.getCategoryName());
                options.add(option);
            }

            log.info("获取到 {} 个产品类别选项", options.size());
            return options;

        } catch (Exception e) {
            log.error("获取产品类别选项失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 验证Excel表头和数据的一致性
     */
    private void validateExcelDataConsistency(List<List<String>> headList, List<List<String>> dataRows, String sheetName) {
        if (CollUtil.isEmpty(headList) || CollUtil.isEmpty(dataRows)) {
            log.warn("工作表 {} 的表头或数据为空", sheetName);
            return;
        }

        int headerColumnCount = headList.size();
        log.info("工作表 {} 表头列数: {}", sheetName, headerColumnCount);

        // 检查每行数据的列数是否与表头一致
        for (int i = 0; i < dataRows.size(); i++) {
            List<String> row = dataRows.get(i);
            if (row.size() != headerColumnCount) {
                log.error("工作表 {} 第 {} 行数据列数不匹配 - 表头: {} 列, 数据: {} 列",
                    sheetName, i + 1, headerColumnCount, row.size());

                // 输出详细的列信息用于调试
                log.debug("表头列名: {}", headList.stream()
                    .map(header -> header.get(0))
                    .collect(Collectors.toList()));
                log.debug("数据行内容: {}", row);

                throw new RuntimeException(String.format(
                    "工作表 %s 第 %d 行数据列数不匹配，表头 %d 列，数据 %d 列",
                    sheetName, i + 1, headerColumnCount, row.size()));
            }
        }

        log.info("工作表 {} 数据一致性验证通过，共 {} 行数据，每行 {} 列",
            sheetName, dataRows.size(), headerColumnCount);
    }
}